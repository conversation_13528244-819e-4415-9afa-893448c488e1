//
//  File 2.swift
//
//
//  Created by zwt on 2024/4/9.
//

import Alamofire
import Foundation
import Moya
import UIKit

enum NetworkURL: String {
    case sanva = "https://advisor.sanva.tk/v2"
    case sanvaCn = "https://advisor.sanva.top/v2"
    case test = "http://10.0.136.252:33001"

    var url: URL {
        URL(string: rawValue)!
    }

    var urlString: String {
        rawValue
    }

    static var current: URL {
        #if DEBUG
            // {{ AURA-X: Add - 支持调试模式的端点切换 }}
            if Preferences.debugModeEnabled.value {
                let debugEndpoint = Preferences.debugSelectedEndpoint.value
                switch debugEndpoint {
                case "sanva":
                    return NetworkURL.sanva.url
                case "sanvaCn":
                    return NetworkURL.sanvaCn.url
                case "test":
                    return NetworkURL.test.url
                default:
                    break
                }
            }

            return Preferences.isRelease.value ? (BootManager.shared.isChina ? NetworkURL.sanvaCn.url : NetworkURL.sanva.url) : NetworkURL.test.url
        #else
            BootManager.shared.isChina ? NetworkURL.sanvaCn.url : NetworkURL.sanva.url
        #endif
    }

    static var currentURLString: String {
        #if DEBUG
            // {{ AURA-X: Add - 支持调试模式的端点切换 }}
            if Preferences.debugModeEnabled.value {
                let debugEndpoint = Preferences.debugSelectedEndpoint.value
                switch debugEndpoint {
                case "sanva":
                    return NetworkURL.sanva.urlString
                case "sanvaCn":
                    return NetworkURL.sanvaCn.urlString
                case "test":
                    return NetworkURL.test.urlString
                default:
                    break
                }
            }

            return Preferences.isRelease.value ? (BootManager.shared.isChina ? NetworkURL.sanvaCn.urlString : NetworkURL.sanva.urlString) : NetworkURL.test.urlString
        #else
            BootManager.shared.isChina ? NetworkURL.sanvaCn.urlString : NetworkURL.sanva.urlString
        #endif
    }

    static var chat: URL {
        URL(string: "\(currentURLString)/chat")!
    }

    static var generateTitle: URL {
        URL(string: "\(currentURLString)/generateTitle")!
    }

    static var privacyPolicy: URL {
        URL(string: "\(currentURLString)/privacy.html?lang=\(Locale.preferredLanguages.first?.components(separatedBy: "-").first ?? "en")")!
    }

    static var userTerms: URL {
        URL(string: "\(currentURLString)/userTerms.html?lang=\(Locale.preferredLanguages.first?.components(separatedBy: "-").first ?? "en")")!
    }
}

enum APIBase {
    static func commonHeaders() -> [String: String] {
        var headers = [
            "Content-Type": "application/json",
            "App-Version": Bundle.main.object(forInfoDictionaryKey: "CFBundleShortVersionString") as? String ?? "unknown",
            "OS-Version": UIDevice.current.systemVersion,
            "Device-Model": UIDevice.current.model,
            "Time-Zone": TimeZone.current.identifier,
            "Network-Type": getNetworkType(),
            "Screen-Resolution": "\(UIScreen.main.bounds.width)x\(UIScreen.main.bounds.height)",
            "Device-Orientation": UIDevice.current.orientation.isLandscape ? "Landscape" : "Portrait",
            "local": Locale.current.identifier.components(separatedBy: "-").first ?? "en",
        ]

        if let token = AccountManager.shared.currentUser?.token {
            headers["Authorization"] = "Bearer \(token)"
        }

        return headers
    }

    private static func getNetworkType() -> String {
        let reachability = NetworkReachabilityManager()
        if reachability?.isReachableOnCellular == true {
            return "Cellular"
        } else if reachability?.isReachableOnEthernetOrWiFi == true {
            return "WiFi"
        } else {
            return "Unknown"
        }
    }
}

enum NetworkStatus {
    case success(Int)
    case failure(NetworkError)
}

public enum NetworkError: Swift.Error {
    /// 未知
    case unknown
    /// 通用
    case response(Int, String?)
    /// 无网络
    case lostConnect
    /// 超时
    case timeout
    /// 文件不存在
    case fileNotFound
    /// 取消
    case cancelled
    /// 5XX错误
    case hostError(String?)
    /// 4XX错误
    case badRequest(String?)

    var errorCode: Int {
        switch self {
        case let .response(code, _):
            code
        default:
            0
        }
    }

    var errorMessage: String? {
        switch self {
        case let .response(_, msg):
            msg
        case let .hostError(msg):
            msg
        case let .badRequest(msg):
            msg
        default:
            nil
        }
    }

    init(with moya: Moya.MoyaError) {
        switch moya {
        case let .underlying(e, r):
            if let error = e.asAFError {
                self = .init(with: error)
            } else if let res = r {
                self = Self.convertCode(res.statusCode, desc: res.description)
            } else {
                self = .unknown
            }
        case let .statusCode(r):
            self = Self.convertCode(r.statusCode, desc: r.description)
        case let .jsonMapping(res):
            self = Self.convertCode(res.statusCode, desc: res.description)
        default:
            self = .unknown
        }
    }

    init(with af: AFError?) {
        if let error = af?.underlyingError as? NSError {
            self = Self.convertError(error)
        } else if case .explicitlyCancelled = af {
            self = .cancelled
        } else if let code = af?.responseCode {
            self = Self.convertCode(code)
        } else {
            self = .unknown
        }
    }

    init(error: NSError?) {
        if let err = error {
            self = Self.convertError(err)
        } else {
            self = .unknown
        }
    }

    init(statusCode: Int, desc: String? = nil) {
        self = Self.convertCode(statusCode, desc: desc)
    }

    private static func convertCode(_ code: Int, desc: String? = nil) -> NetworkError {
        if code >= 400, code <= 499 {
            .badRequest(desc)
        } else if code >= 500, code <= 599 {
            .hostError(desc)
        } else {
            .response(code, desc)
        }
    }

    private static func convertError(_ error: NSError) -> NetworkError {
        if error.code == NSURLErrorTimedOut {
            .timeout
        } else if error.code == NSURLErrorNetworkConnectionLost ||
            error.code == NSURLErrorNotConnectedToInternet ||
            error.code == NSURLErrorDataNotAllowed
        {
            .lostConnect
        } else if error.code == NSURLErrorCancelled {
            .cancelled
        } else if error.code == NSURLErrorCannotConnectToHost ||
            error.code == NSURLErrorCannotFindHost
        {
            .hostError(error.localizedDescription)
        } else {
            convertCode(error.code, desc: error.localizedDescription)
        }
    }
}

extension NetworkError {
    var isTimeout: Bool {
        if case .timeout = self { return true }
        return false
    }

    var isLostConnect: Bool {
        if case .lostConnect = self { return true }
        return false
    }

    var isFileNotFound: Bool {
        if case .fileNotFound = self { return true }
        return false
    }

    var isCancelled: Bool {
        if case .cancelled = self { return true }
        return false
    }

    var isHostError: Bool {
        if case .hostError = self { return true }
        return false
    }

    var isBadRequest: Bool {
        if case .badRequest = self { return true }
        return false
    }

    /// 网络错误
    var isNetworkError: Bool {
        if isTimeout || isHostError || isLostConnect {
            return true
        }
        return false
    }

    var asNSErrorCode: Int {
        if case let .response(code, _) = self { return code }
        if isLostConnect { return NSURLErrorNetworkConnectionLost }
        if isTimeout { return NSURLErrorTimedOut }
        if isCancelled { return NSURLErrorCancelled }
        if isHostError { return NSURLErrorCannotFindHost }
        if isBadRequest { return NSURLErrorCannotConnectToHost }
        return NSURLErrorUnknown
    }

    var asNSError: NSError {
        var i: [String: Any] = [:]
        if let msg = errorMessage {
            i["message"] = msg
        }
        let e = NSError(domain: NSURLErrorDomain, code: asNSErrorCode, userInfo: i)
        return e
    }
}
