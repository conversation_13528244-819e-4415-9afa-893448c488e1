//
//  NetworkDebugManager.swift
//  ChatAdvisor
//
//  Created by AI Assistant on 2025/1/29.
//

import Foundation
import OSLog
import Network

private let logger = Logger(subsystem: "com.sanva.chatadvisor", category: "NetworkDebugManager")

/// 网络调试详细结果
struct NetworkDebugResult {
    let endpoint: String
    let url: String
    let responseTime: TimeInterval
    let isSuccess: Bool
    let statusCode: Int?
    let error: String?
    let timestamp: Date
    let serverInfo: [String: Any]?
}

/// 网络调试管理器
class NetworkDebugManager: ObservableObject {
    static let shared = NetworkDebugManager()
    
    @Published var isDebugging = false
    @Published var debugResults: [NetworkDebugResult] = []
    @Published var currentEndpoint: String = ""
    @Published var networkStatus: String = "未知"
    
    private let networkMonitor = NWPathMonitor()
    private let monitorQueue = DispatchQueue(label: "NetworkDebugMonitor")
    
    private init() {
        setupNetworkMonitoring()
        updateCurrentEndpoint()
    }
    
    /// 获取所有可用的端点
    func getAllEndpoints() -> [(name: String, url: String, description: String)] {
        var endpoints: [(name: String, url: String, description: String)] = []
        
        #if DEBUG
        endpoints.append(("test", NetworkURL.test.urlString, "测试环境"))
        #endif
        
        endpoints.append(("sanva", NetworkURL.sanva.urlString, "海外服务器"))
        endpoints.append(("sanvaCn", NetworkURL.sanvaCn.urlString, "中国服务器"))
        
        return endpoints
    }
    
    /// 获取当前使用的端点信息
    func getCurrentEndpointInfo() -> (name: String, url: String, description: String) {
        let currentURL = NetworkURL.currentURLString
        
        #if DEBUG
        if currentURL == NetworkURL.test.urlString {
            return ("test", currentURL, "测试环境")
        }
        #endif
        
        if currentURL == NetworkURL.sanva.urlString {
            return ("sanva", currentURL, "海外服务器")
        } else if currentURL == NetworkURL.sanvaCn.urlString {
            return ("sanvaCn", currentURL, "中国服务器")
        }
        
        return ("unknown", currentURL, "未知端点")
    }
    
    /// 对指定端点执行详细的ping测试
    func pingEndpoint(_ endpoint: String, url: String) async -> NetworkDebugResult {
        logger.info("开始ping测试: \(endpoint) - \(url)")
        
        let startTime = CFAbsoluteTimeGetCurrent()
        
        do {
            guard let pingURL = URL(string: "\(url)/ping") else {
                throw NetworkError.invalidURL
            }
            
            var request = URLRequest(url: pingURL)
            request.httpMethod = "GET"
            request.timeoutInterval = 10.0
            request.cachePolicy = .reloadIgnoringLocalAndRemoteCacheData
            
            // 添加应用信息头
            request.setValue("iOS", forHTTPHeaderField: "platform")
            request.setValue(Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "unknown", forHTTPHeaderField: "app-version")
            
            let (data, response) = try await URLSession.shared.data(for: request)
            
            let endTime = CFAbsoluteTimeGetCurrent()
            let responseTime = (endTime - startTime) * 1000 // 转换为毫秒
            
            let httpResponse = response as? HTTPURLResponse
            let statusCode = httpResponse?.statusCode ?? 0
            let isSuccess = statusCode == 200
            
            var serverInfo: [String: Any]?
            if let data = data {
                do {
                    if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                        serverInfo = json
                    }
                } catch {
                    logger.warning("解析ping响应JSON失败: \(error)")
                }
            }
            
            let result = NetworkDebugResult(
                endpoint: endpoint,
                url: url,
                responseTime: responseTime,
                isSuccess: isSuccess,
                statusCode: statusCode,
                error: nil,
                timestamp: Date(),
                serverInfo: serverInfo
            )
            
            logger.info("Ping测试完成: \(endpoint) - \(Int(responseTime))ms - \(isSuccess ? "成功" : "失败")")
            
            return result
            
        } catch {
            let endTime = CFAbsoluteTimeGetCurrent()
            let responseTime = (endTime - startTime) * 1000
            
            let result = NetworkDebugResult(
                endpoint: endpoint,
                url: url,
                responseTime: responseTime,
                isSuccess: false,
                statusCode: nil,
                error: error.localizedDescription,
                timestamp: Date(),
                serverInfo: nil
            )
            
            logger.error("Ping测试失败: \(endpoint) - \(error.localizedDescription)")
            
            return result
        }
    }
    
    /// 对所有端点执行ping测试
    func pingAllEndpoints() async {
        await MainActor.run {
            isDebugging = true
            debugResults.removeAll()
        }
        
        let endpoints = getAllEndpoints()
        
        for endpoint in endpoints {
            let result = await pingEndpoint(endpoint.name, url: endpoint.url)
            
            await MainActor.run {
                debugResults.append(result)
            }
        }
        
        await MainActor.run {
            isDebugging = false
        }
    }
    
    /// 切换到指定端点（仅在DEBUG模式下有效）
    func switchToEndpoint(_ endpointName: String) {
        #if DEBUG
        Preferences.debugSelectedEndpoint.value = endpointName
        Preferences.debugModeEnabled.value = true
        updateCurrentEndpoint()
        logger.info("切换到调试端点: \(endpointName)")
        #else
        logger.warning("端点切换仅在DEBUG模式下可用")
        #endif
    }
    
    /// 重置到自动端点选择
    func resetToAutoEndpoint() {
        Preferences.debugModeEnabled.value = false
        Preferences.debugSelectedEndpoint.value = ""
        updateCurrentEndpoint()
        logger.info("重置为自动端点选择")
    }
    
    /// 获取网络诊断信息
    func getNetworkDiagnostics() -> [String: Any] {
        var diagnostics: [String: Any] = [:]
        
        // 基本网络状态
        diagnostics["networkStatus"] = networkStatus
        diagnostics["currentEndpoint"] = getCurrentEndpointInfo()
        
        // 设备信息
        diagnostics["deviceInfo"] = [
            "model": UIDevice.current.model,
            "systemName": UIDevice.current.systemName,
            "systemVersion": UIDevice.current.systemVersion,
            "isPad": UIDevice.current.isPad
        ]
        
        // 应用信息
        diagnostics["appInfo"] = [
            "version": Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "unknown",
            "build": Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "unknown",
            "bundleId": Bundle.main.bundleIdentifier ?? "unknown"
        ]
        
        // 调试状态
        diagnostics["debugInfo"] = [
            "debugModeEnabled": Preferences.debugModeEnabled.value,
            "debugSelectedEndpoint": Preferences.debugSelectedEndpoint.value,
            "isRelease": Preferences.isRelease.value
        ]
        
        return diagnostics
    }
    
    // MARK: - Private Methods
    
    private func setupNetworkMonitoring() {
        networkMonitor.pathUpdateHandler = { [weak self] path in
            DispatchQueue.main.async {
                self?.updateNetworkStatus(path)
            }
        }
        networkMonitor.start(queue: monitorQueue)
    }
    
    private func updateNetworkStatus(_ path: NWPath) {
        switch path.status {
        case .satisfied:
            if path.usesInterfaceType(.wifi) {
                networkStatus = "WiFi连接"
            } else if path.usesInterfaceType(.cellular) {
                networkStatus = "蜂窝网络"
            } else {
                networkStatus = "网络已连接"
            }
        case .unsatisfied:
            networkStatus = "网络未连接"
        case .requiresConnection:
            networkStatus = "需要连接"
        @unknown default:
            networkStatus = "未知状态"
        }
    }
    
    private func updateCurrentEndpoint() {
        let info = getCurrentEndpointInfo()
        currentEndpoint = "\(info.name) (\(info.description))"
    }
}

// MARK: - NetworkError
enum NetworkError: Error, LocalizedError {
    case invalidURL
    case noData
    case decodingError
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "无效的URL"
        case .noData:
            return "没有数据"
        case .decodingError:
            return "数据解析错误"
        }
    }
}
