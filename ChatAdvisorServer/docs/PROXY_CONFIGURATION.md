# 代理配置指南

本文档说明如何在 ChatAdvisorServer 中配置和使用代理。

## 概述

ChatAdvisorServer 支持环境感知的代理配置，确保代理仅在开发环境中启用，在生产环境中自动禁用。

## 核心特性

- **环境感知**: 根据 `NODE_ENV` 自动判断是否启用代理
- **开发环境**: 支持代理配置，便于本地开发和调试
- **生产环境**: 自动禁用代理，直接访问外部API
- **安全性**: 防止生产环境意外使用代理

## 配置方法

### 1. 开发环境配置

在 `.env.development` 文件中配置代理：

```bash
# 代理配置 - 仅在开发环境启用
HTTP_PROXY=http://127.0.0.1:7890
HTTPS_PROXY=http://127.0.0.1:7890
```

### 2. 生产环境配置

在 `.env.production` 文件中，代理被明确禁用：

```bash
# 代理配置 - 生产环境禁用代理
# HTTP_PROXY=
# HTTPS_PROXY=
# 注意：生产环境不使用代理，直接访问外部API
```

### 3. 使用脚本配置

使用提供的脚本来管理代理配置：

```bash
# 检查当前代理配置
./scripts/proxy-config.sh check

# 设置代理（仅在开发环境生效）
./scripts/proxy-config.sh set http://127.0.0.1:7890

# 测试代理连接
./scripts/proxy-config.sh test

# 清除代理配置
./scripts/proxy-config.sh clear
```

## 环境判断逻辑

### 代理启用条件

代理仅在以下条件**同时满足**时启用：

1. `NODE_ENV=development`
2. 配置了 `HTTP_PROXY` 或 `HTTPS_PROXY` 环境变量

### 不同环境的行为

| 环境 | NODE_ENV | 代理状态 | 说明 |
|------|----------|----------|------|
| 开发环境 | development | ✅ 启用 | 如果配置了代理环境变量 |
| 生产环境 | production | ❌ 禁用 | 始终禁用，直接访问API |
| 测试环境 | test | ❌ 禁用 | 始终禁用 |

## 代码实现

### 代理配置模块

`src/config/proxyConfig.ts` 提供了以下功能：

- `getProxyConfig()`: 获取当前环境的代理配置
- `isProxyEnabled()`: 检查代理是否启用
- `getProxyUrl()`: 获取代理URL
- `setupEnvironmentProxy()`: 设置环境变量
- `getProxyInfo()`: 获取详细信息

### 在代码中使用

```typescript
import { getProxyConfig } from '../config/proxyConfig';

// 获取代理配置
const proxyConfig = getProxyConfig();

if (proxyConfig.enabled && proxyConfig.url) {
    // 使用代理
    const proxyAgent = new undici.ProxyAgent(proxyConfig.url);
    // ... 配置网络请求
} else {
    // 不使用代理
}
```

## 测试和验证

### 运行测试脚本

```bash
# 测试代理配置在不同环境下的行为
node scripts/test-proxy-config.js
```

### 手动验证

1. **开发环境测试**:
   ```bash
   NODE_ENV=development npm start
   # 检查日志中的代理信息
   ```

2. **生产环境测试**:
   ```bash
   NODE_ENV=production npm start
   # 确认代理被禁用
   ```

## 常见问题

### Q: 为什么生产环境不使用代理？

A: 生产环境通常部署在云服务器上，应该直接访问外部API。使用代理可能会：
- 增加延迟
- 引入单点故障
- 带来安全风险
- 增加配置复杂性

### Q: 如何在开发环境中临时禁用代理？

A: 有几种方法：
1. 注释掉 `.env.development` 中的代理配置
2. 使用脚本清除代理：`./scripts/proxy-config.sh clear`
3. 设置环境变量：`HTTP_PROXY= HTTPS_PROXY= npm start`

### Q: 代理配置不生效怎么办？

A: 检查以下几点：
1. 确认 `NODE_ENV=development`
2. 确认 `.env.development` 中有正确的代理配置
3. 重启应用
4. 检查代理服务是否正常运行

### Q: 如何验证代理是否正常工作？

A: 查看应用启动日志，会显示代理配置信息：
```
=== 代理配置信息 ===
环境: development
代理状态: 启用
代理URL: http://127.0.0.1:7890
```

## 最佳实践

1. **开发环境**: 根据需要配置代理，便于调试和访问外部API
2. **生产环境**: 始终禁用代理，确保直接访问
3. **配置管理**: 使用环境文件管理不同环境的配置
4. **安全性**: 不要在代码中硬编码代理配置
5. **监控**: 在日志中记录代理使用情况，便于问题排查

## 相关文件

- `src/config/proxyConfig.ts` - 代理配置模块
- `.env.development` - 开发环境配置
- `.env.production` - 生产环境配置
- `scripts/proxy-config.sh` - 代理配置脚本
- `scripts/test-proxy-config.js` - 测试脚本
