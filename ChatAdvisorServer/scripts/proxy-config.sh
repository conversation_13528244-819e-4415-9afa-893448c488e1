#!/bin/bash

# 代理配置脚本
# 用于配置HTTP/HTTPS代理来访问xAI API
# 注意：代理配置仅在开发环境中生效

echo "🔧 xAI API 代理配置脚本 (环境感知版本)"
echo "======================================="

# 检查当前环境
check_environment() {
    local node_env="${NODE_ENV:-development}"
    echo "当前环境: $node_env"

    if [[ "$node_env" != "development" ]]; then
        echo "⚠️  警告: 当前环境为 $node_env，代理配置仅在开发环境中生效"
        echo "   生产环境应直接访问外部API，不使用代理"
        return 1
    fi
    return 0
}

# 检查是否有代理配置
check_proxy() {
    echo "当前代理配置："
    echo "HTTP_PROXY: ${HTTP_PROXY:-未设置}"
    echo "HTTPS_PROXY: ${HTTPS_PROXY:-未设置}"
    echo "NO_PROXY: ${NO_PROXY:-未设置}"
}

# 设置代理
set_proxy() {
    local proxy_url="$1"

    if [[ -z "$proxy_url" ]]; then
        echo "请提供代理URL，格式：http://proxy-server:port"
        echo "示例：http://127.0.0.1:7890"
        return 1
    fi

    # 检查环境
    if ! check_environment; then
        echo "❌ 代理配置失败：仅在开发环境中允许设置代理"
        return 1
    fi

    echo "设置代理为: $proxy_url"

    # 设置环境变量
    export HTTP_PROXY="$proxy_url"
    export HTTPS_PROXY="$proxy_url"
    export http_proxy="$proxy_url"
    export https_proxy="$proxy_url"

    # 添加到开发环境配置文件
    local env_file=".env.development"

    # 检查文件是否存在
    if [[ ! -f "$env_file" ]]; then
        echo "❌ 错误: $env_file 文件不存在"
        return 1
    fi

    # 移除现有的代理配置
    sed -i '' '/^HTTP_PROXY=/d' "$env_file"
    sed -i '' '/^HTTPS_PROXY=/d' "$env_file"
    sed -i '' '/^# 代理配置/d' "$env_file"

    # 添加新的代理配置
    echo "" >> "$env_file"
    echo "# 代理配置 - 仅在开发环境启用" >> "$env_file"
    echo "HTTP_PROXY=$proxy_url" >> "$env_file"
    echo "HTTPS_PROXY=$proxy_url" >> "$env_file"

    echo "✅ 代理配置已设置到 $env_file"
    echo "   重启应用后生效"
}

# 测试代理连接
test_proxy() {
    echo "🔍 测试代理连接..."
    
    # 测试基本连接
    if curl -s --connect-timeout 10 --proxy "$HTTPS_PROXY" https://api.x.ai/v1/ > /dev/null; then
        echo "✅ 代理连接测试成功"
        return 0
    else
        echo "❌ 代理连接测试失败"
        return 1
    fi
}

# 清除代理
clear_proxy() {
    echo "清除代理配置..."

    unset HTTP_PROXY
    unset HTTPS_PROXY
    unset http_proxy
    unset https_proxy

    # 从开发环境配置文件中移除代理配置
    local env_file=".env.development"
    if [[ -f "$env_file" ]]; then
        sed -i '' '/^HTTP_PROXY=/d' "$env_file"
        sed -i '' '/^HTTPS_PROXY=/d' "$env_file"
        sed -i '' '/^# 代理配置/d' "$env_file"
        echo "✅ 已从 $env_file 中移除代理配置"
    fi

    # 也从通用.env文件中移除（如果存在）
    if [[ -f ".env" ]]; then
        sed -i '' '/^HTTP_PROXY=/d' .env
        sed -i '' '/^HTTPS_PROXY=/d' .env
        echo "✅ 已从 .env 中移除代理配置"
    fi

    echo "✅ 代理配置已清除，重启应用后生效"
}

# 显示帮助
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  check                    检查当前代理配置"
    echo "  set <proxy_url>          设置代理"
    echo "  test                     测试代理连接"
    echo "  clear                    清除代理配置"
    echo "  help                     显示帮助"
    echo ""
    echo "示例:"
    echo "  $0 set http://127.0.0.1:7890"
    echo "  $0 test"
    echo "  $0 clear"
}

# 主函数
case "$1" in
    "check")
        check_proxy
        ;;
    "set")
        set_proxy "$2"
        ;;
    "test")
        test_proxy
        ;;
    "clear")
        clear_proxy
        ;;
    "help"|"")
        show_help
        ;;
    *)
        echo "未知选项: $1"
        show_help
        exit 1
        ;;
esac
