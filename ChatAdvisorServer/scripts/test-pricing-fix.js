/**
 * 测试聊天扣费逻辑修复效果
 * 验证价格计算精度和默认价格设置
 */

// 模拟修复前的计算方式
function calculateOldWay(tokenLength, responseLength) {
    const OLD_DEFAULT_PRICE = 5 / 3000; // 旧的默认价格
    const count = 3000;
    
    const consumption = parseFloat(((tokenLength / count) * OLD_DEFAULT_PRICE).toFixed(4));
    const responseConsumption = parseFloat(((responseLength / count) * OLD_DEFAULT_PRICE).toFixed(4));
    
    return {
        inputCost: consumption,
        outputCost: responseConsumption,
        totalCost: consumption + responseConsumption
    };
}

// 模拟修复后的计算方式
function calculateNewWay(tokenLength, responseLength) {
    // 新的默认价格：与配置文件一致
    const DEFAULT_INPUT_PRICE = 4.9 / 3000;
    const DEFAULT_OUTPUT_PRICE = 5.7 / 3000;
    const count = 3000;
    
    // 改进的精度计算
    const rawConsumption = (tokenLength / count) * DEFAULT_INPUT_PRICE;
    const consumption = Math.round(rawConsumption * 1000000) / 1000000;
    
    const rawResponseConsumption = (responseLength / count) * DEFAULT_OUTPUT_PRICE;
    const responseConsumption = Math.round(rawResponseConsumption * 1000000) / 1000000;
    
    return {
        inputCost: consumption,
        outputCost: responseConsumption,
        totalCost: consumption + responseConsumption
    };
}

// 测试不同的token长度
const testCases = [
    { tokenLength: 100, responseLength: 50, description: "短消息" },
    { tokenLength: 500, responseLength: 200, description: "中等消息" },
    { tokenLength: 1000, responseLength: 500, description: "长消息" },
    { tokenLength: 50, responseLength: 20, description: "极短消息" },
    { tokenLength: 3000, responseLength: 1500, description: "超长消息" }
];

console.log("🔧 聊天扣费逻辑修复效果测试\n");
console.log("=" * 80);

testCases.forEach((testCase, index) => {
    console.log(`\n📝 测试案例 ${index + 1}: ${testCase.description}`);
    console.log(`   输入Token: ${testCase.tokenLength}, 输出Token: ${testCase.responseLength}`);
    
    const oldResult = calculateOldWay(testCase.tokenLength, testCase.responseLength);
    const newResult = calculateNewWay(testCase.tokenLength, testCase.responseLength);
    
    console.log(`\n   修复前:`);
    console.log(`     输入费用: ${oldResult.inputCost.toFixed(6)} CNY`);
    console.log(`     输出费用: ${oldResult.outputCost.toFixed(6)} CNY`);
    console.log(`     总费用:   ${oldResult.totalCost.toFixed(6)} CNY`);
    
    console.log(`\n   修复后:`);
    console.log(`     输入费用: ${newResult.inputCost.toFixed(6)} CNY`);
    console.log(`     输出费用: ${newResult.outputCost.toFixed(6)} CNY`);
    console.log(`     总费用:   ${newResult.totalCost.toFixed(6)} CNY`);
    
    // 检查是否为0的问题
    const oldIsZero = oldResult.totalCost === 0;
    const newIsZero = newResult.totalCost === 0;
    
    if (oldIsZero && !newIsZero) {
        console.log(`   ✅ 修复成功: 解决了费用为0的问题`);
    } else if (!oldIsZero && !newIsZero) {
        const improvement = ((newResult.totalCost - oldResult.totalCost) / oldResult.totalCost * 100).toFixed(2);
        console.log(`   📊 费用变化: ${improvement}%`);
    } else if (newIsZero) {
        console.log(`   ⚠️  警告: 费用仍为0，可能需要进一步检查`);
    }
    
    console.log(`   -`.repeat(40));
});

console.log(`\n📊 价格配置对比:`);
console.log(`   旧默认价格: ${(5/3000).toFixed(6)} CNY/3000字符`);
console.log(`   新输入价格: ${(4.9/3000).toFixed(6)} CNY/3000字符`);
console.log(`   新输出价格: ${(5.7/3000).toFixed(6)} CNY/3000字符`);

console.log(`\n🎯 修复要点:`);
console.log(`   1. 提高了默认价格，避免计算结果为0`);
console.log(`   2. 改进了数值计算精度，使用Math.round代替toFixed+parseFloat`);
console.log(`   3. 区分了输入和输出价格，更符合实际定价策略`);
console.log(`   4. 与配置文件中的价格设置保持一致`);

console.log(`\n✅ 测试完成！请检查实际运行效果。`);
