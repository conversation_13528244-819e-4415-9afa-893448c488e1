#!/usr/bin/env node

/**
 * 测试价格管理系统的完整链路
 * 包括：创建模型、检查零价格、修复零价格、验证计费逻辑
 */

const mongoose = require('mongoose');
const Pricing = require('../src/models/Pricing');
require('dotenv').config();

// 连接数据库
async function connectDB() {
    try {
        await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/chatadvisor');
        console.log('✅ 数据库连接成功');
    } catch (error) {
        console.error('❌ 数据库连接失败:', error);
        process.exit(1);
    }
}

// 测试创建零价格模型
async function testCreateZeroPricingModel() {
    console.log('\n🧪 测试创建零价格模型...');
    
    const testModel = {
        modelName: 'test-zero-pricing-model',
        inPrice: 0,
        outPrice: 0,
        count: 3000,
        alias: new Map([
            ['zh', '测试零价格模型'],
            ['en', 'Test Zero Pricing Model']
        ]),
        intro: new Map([
            ['zh', '这是一个用于测试零价格修复功能的模型'],
            ['en', 'This is a test model for zero pricing fix functionality']
        ])
    };

    try {
        // 删除可能存在的测试模型
        await Pricing.deleteOne({ modelName: testModel.modelName });
        
        // 创建新的测试模型
        const pricing = new Pricing(testModel);
        await pricing.save();
        
        console.log('✅ 成功创建零价格测试模型:', testModel.modelName);
        return pricing;
    } catch (error) {
        console.error('❌ 创建测试模型失败:', error);
        throw error;
    }
}

// 测试查找零价格模型
async function testFindZeroPricingModels() {
    console.log('\n🔍 查找零价格模型...');
    
    const zeroPricingModels = await Pricing.find({
        $or: [
            { inPrice: 0 },
            { outPrice: 0 }
        ]
    });

    console.log(`📊 发现 ${zeroPricingModels.length} 个零价格模型:`);
    zeroPricingModels.forEach(model => {
        console.log(`  - ${model.modelName}: inPrice=${model.inPrice}, outPrice=${model.outPrice}`);
    });

    return zeroPricingModels;
}

// 测试修复零价格
async function testFixZeroPricing() {
    console.log('\n🔧 测试修复零价格功能...');
    
    const DEFAULT_PRICE = 5 / 3000; // 5每3000字符
    
    // 查找价格为0的模型
    const zeroPricingModels = await Pricing.find({
        $or: [
            { inPrice: 0 },
            { outPrice: 0 }
        ]
    });

    if (zeroPricingModels.length === 0) {
        console.log('✅ 没有发现零价格模型');
        return { fixed: 0 };
    }

    // 批量更新价格为0的模型
    const bulkOps = zeroPricingModels.map(model => ({
        updateOne: {
            filter: { _id: model._id },
            update: {
                $set: {
                    inPrice: model.inPrice === 0 ? DEFAULT_PRICE : model.inPrice,
                    outPrice: model.outPrice === 0 ? DEFAULT_PRICE : model.outPrice
                }
            }
        }
    }));

    const result = await Pricing.bulkWrite(bulkOps);
    
    console.log(`✅ 成功修复 ${result.modifiedCount} 个模型的零价格问题`);
    console.log(`📝 默认价格设置为: ${DEFAULT_PRICE.toFixed(6)} 每字符`);
    
    return {
        fixed: result.modifiedCount,
        defaultPrice: DEFAULT_PRICE,
        models: zeroPricingModels.map(m => m.modelName)
    };
}

// 测试价格计算逻辑
async function testPricingCalculation() {
    console.log('\n💰 测试价格计算逻辑...');
    
    // 获取一个测试模型
    const testModel = await Pricing.findOne({ modelName: 'test-zero-pricing-model' });
    if (!testModel) {
        console.log('⚠️  未找到测试模型，跳过价格计算测试');
        return;
    }

    // 使用与配置文件一致的默认价格：基于 gpt-4o-mini 的价格配置
    // profitRate = 0.045, exchangeRate = 100
    // inputPrice = (0.004 + 0.045) * 100 = 4.9 每3000字符
    // outputPrice = (0.004 * 3 + 0.045) * 100 = 5.7 每3000字符
    const DEFAULT_INPUT_PRICE = 4.9 / 3000; // 与配置文件保持一致
    const DEFAULT_OUTPUT_PRICE = 5.7 / 3000; // 与配置文件保持一致
    
    // 模拟价格计算逻辑
    const effectiveInPrice = testModel.inPrice === 0 ? DEFAULT_INPUT_PRICE : testModel.inPrice;
    const effectiveOutPrice = testModel.outPrice === 0 ? DEFAULT_OUTPUT_PRICE : testModel.outPrice;
    
    console.log(`📊 模型: ${testModel.modelName}`);
    console.log(`  原始价格: inPrice=${testModel.inPrice}, outPrice=${testModel.outPrice}`);
    console.log(`  有效价格: inPrice=${effectiveInPrice.toFixed(6)}, outPrice=${effectiveOutPrice.toFixed(6)}`);
    
    // 模拟计算费用
    const tokenLength = 1000; // 假设1000个token
    const responseLength = 500; // 假设500个token的回复
    
    const inputCost = (tokenLength / testModel.count) * effectiveInPrice;
    const outputCost = (responseLength / testModel.count) * effectiveOutPrice;
    const totalCost = inputCost + outputCost;
    
    console.log(`💸 费用计算 (基于 ${tokenLength} 输入token, ${responseLength} 输出token):`);
    console.log(`  输入费用: ${inputCost.toFixed(6)}`);
    console.log(`  输出费用: ${outputCost.toFixed(6)}`);
    console.log(`  总费用: ${totalCost.toFixed(6)}`);
    
    if (testModel.inPrice === 0 || testModel.outPrice === 0) {
        console.log('⚠️  检测到零价格，已使用默认价格进行计算');
    }
}

// 清理测试数据
async function cleanup() {
    console.log('\n🧹 清理测试数据...');
    
    try {
        await Pricing.deleteOne({ modelName: 'test-zero-pricing-model' });
        console.log('✅ 测试数据清理完成');
    } catch (error) {
        console.error('❌ 清理测试数据失败:', error);
    }
}

// 主测试函数
async function runTests() {
    try {
        await connectDB();
        
        console.log('🚀 开始测试价格管理系统...');
        
        // 1. 创建零价格测试模型
        await testCreateZeroPricingModel();
        
        // 2. 查找零价格模型
        await testFindZeroPricingModels();
        
        // 3. 测试修复零价格
        const fixResult = await testFixZeroPricing();
        
        // 4. 验证修复后的价格
        await testFindZeroPricingModels();
        
        // 5. 测试价格计算逻辑
        await testPricingCalculation();
        
        // 6. 清理测试数据
        await cleanup();
        
        console.log('\n✅ 所有测试完成！');
        console.log('\n📋 测试总结:');
        console.log(`  - 修复了 ${fixResult.fixed} 个零价格模型`);
        console.log(`  - 默认价格: ${fixResult.defaultPrice?.toFixed(6) || 'N/A'} 每字符`);
        console.log('  - 价格计算逻辑正常工作');
        console.log('  - 零价格检测和修复功能正常');
        
    } catch (error) {
        console.error('❌ 测试失败:', error);
    } finally {
        await mongoose.disconnect();
        console.log('🔌 数据库连接已断开');
    }
}

// 运行测试
if (require.main === module) {
    runTests();
}

module.exports = {
    testCreateZeroPricingModel,
    testFindZeroPricingModels,
    testFixZeroPricing,
    testPricingCalculation,
    cleanup
};
