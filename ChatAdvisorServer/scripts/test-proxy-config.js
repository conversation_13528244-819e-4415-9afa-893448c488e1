#!/usr/bin/env node

/**
 * 代理配置测试脚本
 * 验证代理配置在不同环境下的行为
 */

require('dotenv').config();
const path = require('path');

// 模拟不同环境
const environments = ['development', 'production', 'test'];

console.log('🧪 代理配置测试\n');

// 保存原始环境变量
const originalNodeEnv = process.env.NODE_ENV;
const originalHttpProxy = process.env.HTTP_PROXY;
const originalHttpsProxy = process.env.HTTPS_PROXY;

async function testEnvironment(env) {
    console.log(`\n=== 测试环境: ${env} ===`);
    
    // 设置环境
    process.env.NODE_ENV = env;
    
    // 重新加载环境配置
    delete require.cache[require.resolve('dotenv')];
    require('dotenv').config({ path: path.resolve(process.cwd(), `.env.${env}`) });
    require('dotenv').config({ path: path.resolve(process.cwd(), '.env') });
    
    // 动态导入代理配置模块（避免缓存）
    const modulePath = path.resolve(__dirname, '../src/config/proxyConfig.ts');
    delete require.cache[modulePath];
    
    try {
        // 使用 ts-node 或直接编译后的 JS
        let proxyConfig;
        try {
            // 尝试使用编译后的 JS
            const jsPath = modulePath.replace('.ts', '.js');
            delete require.cache[jsPath];
            proxyConfig = require(jsPath);
        } catch (e) {
            // 如果没有编译版本，使用 ts-node
            require('ts-node/register');
            proxyConfig = require(modulePath);
        }
        
        const config = proxyConfig.getProxyConfig();
        const info = proxyConfig.getProxyInfo();
        
        console.log(`环境变量 NODE_ENV: ${process.env.NODE_ENV}`);
        console.log(`环境变量 HTTP_PROXY: ${process.env.HTTP_PROXY || '未设置'}`);
        console.log(`环境变量 HTTPS_PROXY: ${process.env.HTTPS_PROXY || '未设置'}`);
        console.log(`代理状态: ${config.enabled ? '✅ 启用' : '❌ 禁用'}`);
        
        if (config.enabled) {
            console.log(`代理URL: ${config.url}`);
            console.log(`HTTP代理: ${config.httpProxy || '未设置'}`);
            console.log(`HTTPS代理: ${config.httpsProxy || '未设置'}`);
        }
        
        // 验证预期行为
        const hasProxyConfig = !!(process.env.HTTP_PROXY || process.env.HTTPS_PROXY);
        const expectedEnabled = env === 'development' && hasProxyConfig;
        const actualEnabled = config.enabled;

        if (expectedEnabled === actualEnabled) {
            console.log('✅ 代理配置符合预期');
        } else {
            console.log('❌ 代理配置不符合预期');
            console.log(`   预期: ${expectedEnabled ? '启用' : '禁用'} (环境=${env}, 有代理配置=${hasProxyConfig})`);
            console.log(`   实际: ${actualEnabled ? '启用' : '禁用'}`);
        }
        
    } catch (error) {
        console.log(`❌ 测试失败: ${error.message}`);
    }
}

async function runTests() {
    for (const env of environments) {
        await testEnvironment(env);
    }
    
    // 恢复原始环境变量
    process.env.NODE_ENV = originalNodeEnv;
    if (originalHttpProxy) {
        process.env.HTTP_PROXY = originalHttpProxy;
    } else {
        delete process.env.HTTP_PROXY;
    }
    if (originalHttpsProxy) {
        process.env.HTTPS_PROXY = originalHttpsProxy;
    } else {
        delete process.env.HTTPS_PROXY;
    }
    
    console.log('\n🎉 测试完成');
    console.log('\n📋 总结:');
    console.log('- 开发环境 (development): 如果配置了代理环境变量，则启用代理');
    console.log('- 生产环境 (production): 始终禁用代理');
    console.log('- 测试环境 (test): 始终禁用代理');
    console.log('\n💡 提示:');
    console.log('- 代理配置在 .env.development 文件中设置');
    console.log('- 生产环境不应使用代理，直接访问外部API');
    console.log('- 使用 npm run start:dev 启动开发环境');
    console.log('- 使用 npm run start:prod 启动生产环境');
}

// 运行测试
runTests().catch(console.error);
