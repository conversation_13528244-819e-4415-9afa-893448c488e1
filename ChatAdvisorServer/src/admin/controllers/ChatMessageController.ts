/**
 * 聊天消息管理控制器
 * 提供聊天消息的查询、编辑、删除、统计等功能
 */

import { Request, Response, NextFunction } from 'express';
import { ChatMessageModel, Role } from '../../models/ChatMessage';
import { logger } from '../../business/logger';
import { validationResult } from 'express-validator';

export class ChatMessageController {
    /**
     * 获取聊天消息列表
     */
    public async getMessages(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const {
                page = 1,
                limit = 20,
                search,
                chatId,
                role,
                isComplete,
                dateFrom,
                dateTo,
                sortBy = 'createdTime',
                sortOrder = 'desc'
            } = req.query;

            // 构建查询条件
            const query: any = {};
            
            if (search) {
                query.content = { $regex: search, $options: 'i' };
            }

            if (chatId) {
                query.chatId = chatId;
            }

            if (role && Object.values(Role).includes(role as Role)) {
                query.role = role;
            }

            if (isComplete !== undefined) {
                query.isComplete = isComplete === 'true';
            }

            if (dateFrom || dateTo) {
                query.createdTime = {};
                if (dateFrom) query.createdTime.$gte = new Date(dateFrom as string);
                if (dateTo) query.createdTime.$lte = new Date(dateTo as string);
            }

            // 分页参数
            const pageNum = Math.max(1, parseInt(page as string));
            const limitNum = Math.min(100, Math.max(1, parseInt(limit as string)));
            const skip = (pageNum - 1) * limitNum;

            // 排序
            const sort: any = {};
            sort[sortBy as string] = sortOrder === 'asc' ? 1 : -1;

            // 查询消息
            const [messages, total] = await Promise.all([
                ChatMessageModel.find(query)
                    .sort(sort)
                    .skip(skip)
                    .limit(limitNum)
                    .lean(),
                ChatMessageModel.countDocuments(query)
            ]);

            res.json({
                success: true,
                data: {
                    messages,
                    pagination: {
                        page: pageNum,
                        limit: limitNum,
                        total,
                        pages: Math.ceil(total / limitNum)
                    }
                }
            });

        } catch (error) {
            logger.error('Get messages failed:', error);
            next(error);
        }
    }

    /**
     * 获取消息详情
     */
    public async getMessageById(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { id } = req.params;

            const message = await ChatMessageModel.findById(id).lean();

            if (!message) {
                res.status(404).json({
                    success: false,
                    message: 'Message not found'
                });
                return;
            }

            res.json({
                success: true,
                data: message
            });

        } catch (error) {
            logger.error('Get message by ID failed:', error);
            next(error);
        }
    }

    /**
     * 更新消息内容
     */
    public async updateMessage(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
                return;
            }

            const { id } = req.params;
            const { content, isComplete } = req.body;

            const updateData: any = {};
            if (content !== undefined) updateData.content = content;
            if (isComplete !== undefined) updateData.isComplete = isComplete;

            const message = await ChatMessageModel.findByIdAndUpdate(
                id,
                updateData,
                { new: true, runValidators: true }
            );

            if (!message) {
                res.status(404).json({
                    success: false,
                    message: 'Message not found'
                });
                return;
            }

            logger.info(`Message ${id} updated`);

            res.json({
                success: true,
                data: message,
                message: 'Message updated successfully'
            });

        } catch (error) {
            logger.error('Update message failed:', error);
            next(error);
        }
    }

    /**
     * 删除消息
     */
    public async deleteMessage(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { id } = req.params;

            const message = await ChatMessageModel.findByIdAndDelete(id);

            if (!message) {
                res.status(404).json({
                    success: false,
                    message: 'Message not found'
                });
                return;
            }

            logger.info(`Message ${id} deleted`);

            res.json({
                success: true,
                message: 'Message deleted successfully'
            });

        } catch (error) {
            logger.error('Delete message failed:', error);
            next(error);
        }
    }

    /**
     * 批量删除消息
     */
    public async batchDeleteMessages(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
                return;
            }

            const { messageIds, chatId, dateFrom, dateTo } = req.body;

            let query: any = {};

            if (messageIds && Array.isArray(messageIds)) {
                query._id = { $in: messageIds };
            } else if (chatId) {
                query.chatId = chatId;
                
                if (dateFrom || dateTo) {
                    query.createdTime = {};
                    if (dateFrom) query.createdTime.$gte = new Date(dateFrom);
                    if (dateTo) query.createdTime.$lte = new Date(dateTo);
                }
            } else {
                res.status(400).json({
                    success: false,
                    message: 'Either messageIds or chatId is required'
                });
                return;
            }

            const result = await ChatMessageModel.deleteMany(query);

            logger.info(`Batch deleted ${result.deletedCount} messages`);

            res.json({
                success: true,
                data: {
                    deletedCount: result.deletedCount
                },
                message: `Successfully deleted ${result.deletedCount} messages`
            });

        } catch (error) {
            logger.error('Batch delete messages failed:', error);
            next(error);
        }
    }

    /**
     * 获取聊天会话列表
     */
    public async getChatSessions(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const {
                page = 1,
                limit = 20,
                dateFrom,
                dateTo
            } = req.query;

            // 构建匹配条件
            const matchStage: any = {};
            if (dateFrom || dateTo) {
                matchStage.createdTime = {};
                if (dateFrom) matchStage.createdTime.$gte = new Date(dateFrom as string);
                if (dateTo) matchStage.createdTime.$lte = new Date(dateTo as string);
            }

            // 分页参数
            const pageNum = Math.max(1, parseInt(page as string));
            const limitNum = Math.min(100, Math.max(1, parseInt(limit as string)));
            const skip = (pageNum - 1) * limitNum;

            // 聚合查询获取会话信息
            const pipeline = [
                { $match: matchStage },
                {
                    $group: {
                        _id: '$chatId',
                        messageCount: { $sum: 1 },
                        lastMessage: { $last: '$content' },
                        lastMessageTime: { $last: '$createdTime' },
                        firstMessageTime: { $first: '$createdTime' },
                        roles: { $addToSet: '$role' },
                        completedMessages: {
                            $sum: { $cond: ['$isComplete', 1, 0] }
                        }
                    }
                },
                {
                    $project: {
                        chatId: '$_id',
                        messageCount: 1,
                        lastMessage: {
                            $cond: {
                                if: { $gt: [{ $strLenCP: '$lastMessage' }, 100] },
                                then: { $concat: [{ $substrCP: ['$lastMessage', 0, 97] }, '...'] },
                                else: '$lastMessage'
                            }
                        },
                        lastMessageTime: 1,
                        firstMessageTime: 1,
                        duration: {
                            $subtract: ['$lastMessageTime', '$firstMessageTime']
                        },
                        roles: 1,
                        completedMessages: 1,
                        completionRate: {
                            $multiply: [
                                { $divide: ['$completedMessages', '$messageCount'] },
                                100
                            ]
                        }
                    }
                },
                { $sort: { lastMessageTime: -1 as -1 } },
                { $skip: skip },
                { $limit: limitNum }
            ];

            const [sessions, totalResult] = await Promise.all([
                ChatMessageModel.aggregate(pipeline),
                ChatMessageModel.aggregate([
                    { $match: matchStage },
                    { $group: { _id: '$chatId' } },
                    { $count: 'total' }
                ])
            ]);

            const total = totalResult[0]?.total || 0;

            res.json({
                success: true,
                data: {
                    sessions,
                    pagination: {
                        page: pageNum,
                        limit: limitNum,
                        total,
                        pages: Math.ceil(total / limitNum)
                    }
                }
            });

        } catch (error) {
            logger.error('Get chat sessions failed:', error);
            next(error);
        }
    }

    /**
     * 获取聊天统计信息
     */
    public async getChatStats(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const {
                dateFrom,
                dateTo,
                groupBy = 'day' // day, week, month
            } = req.query;

            const matchStage: any = {};
            if (dateFrom || dateTo) {
                matchStage.createdTime = {};
                if (dateFrom) matchStage.createdTime.$gte = new Date(dateFrom as string);
                if (dateTo) matchStage.createdTime.$lte = new Date(dateTo as string);
            }

            // 根据groupBy参数设置分组格式
            let dateFormat: string;
            switch (groupBy) {
                case 'week':
                    dateFormat = '%Y-%U';
                    break;
                case 'month':
                    dateFormat = '%Y-%m';
                    break;
                default:
                    dateFormat = '%Y-%m-%d';
            }

            // 基础统计
            const [
                totalMessages,
                totalSessions,
                avgMessagesPerSession,
                roleDistribution,
                timeSeriesData
            ] = await Promise.all([
                // 总消息数
                ChatMessageModel.countDocuments(matchStage),

                // 总会话数
                ChatMessageModel.distinct('chatId', matchStage).then(sessions => sessions.length),

                // 平均每会话消息数
                ChatMessageModel.aggregate([
                    { $match: matchStage },
                    { $group: { _id: '$chatId', count: { $sum: 1 } } },
                    { $group: { _id: null, avg: { $avg: '$count' } } }
                ]).then(result => result[0]?.avg || 0),

                // 角色分布
                ChatMessageModel.aggregate([
                    { $match: matchStage },
                    { $group: { _id: '$role', count: { $sum: 1 } } }
                ]),

                // 时间序列数据
                ChatMessageModel.aggregate([
                    { $match: matchStage },
                    {
                        $group: {
                            _id: {
                                date: { $dateToString: { format: dateFormat, date: '$createdTime' } },
                                role: '$role'
                            },
                            count: { $sum: 1 }
                        }
                    },
                    {
                        $group: {
                            _id: '$_id.date',
                            totalMessages: { $sum: '$count' },
                            userMessages: {
                                $sum: {
                                    $cond: [{ $eq: ['$_id.role', 'user'] }, '$count', 0]
                                }
                            },
                            assistantMessages: {
                                $sum: {
                                    $cond: [{ $eq: ['$_id.role', 'assistant'] }, '$count', 0]
                                }
                            }
                        }
                    },
                    { $sort: { _id: 1 } }
                ])
            ]);

            res.json({
                success: true,
                data: {
                    overview: {
                        totalMessages,
                        totalSessions,
                        avgMessagesPerSession: Math.round(avgMessagesPerSession * 100) / 100
                    },
                    roleDistribution,
                    timeSeriesData,
                    groupBy,
                    dateRange: {
                        from: dateFrom,
                        to: dateTo
                    }
                }
            });

        } catch (error) {
            logger.error('Get chat stats failed:', error);
            next(error);
        }
    }

    /**
     * 搜索消息内容
     */
    public async searchMessages(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const {
                keyword,
                page = 1,
                limit = 20,
                chatId,
                role,
                dateFrom,
                dateTo
            } = req.query;

            if (!keyword) {
                res.status(400).json({
                    success: false,
                    message: 'Keyword is required'
                });
                return;
            }

            // 构建查询条件
            const query: any = {
                content: { $regex: keyword, $options: 'i' }
            };

            if (chatId) query.chatId = chatId;
            if (role && Object.values(Role).includes(role as Role)) {
                query.role = role;
            }

            if (dateFrom || dateTo) {
                query.createdTime = {};
                if (dateFrom) query.createdTime.$gte = new Date(dateFrom as string);
                if (dateTo) query.createdTime.$lte = new Date(dateTo as string);
            }

            // 分页参数
            const pageNum = Math.max(1, parseInt(page as string));
            const limitNum = Math.min(100, Math.max(1, parseInt(limit as string)));
            const skip = (pageNum - 1) * limitNum;

            // 搜索消息
            const [messages, total] = await Promise.all([
                ChatMessageModel.find(query)
                    .sort({ createdTime: -1 })
                    .skip(skip)
                    .limit(limitNum)
                    .lean(),
                ChatMessageModel.countDocuments(query)
            ]);

            // 高亮关键词
            const highlightedMessages = messages.map(message => ({
                ...message,
                highlightedContent: this.highlightKeyword(message.content, keyword as string)
            }));

            res.json({
                success: true,
                data: {
                    messages: highlightedMessages,
                    keyword,
                    pagination: {
                        page: pageNum,
                        limit: limitNum,
                        total,
                        pages: Math.ceil(total / limitNum)
                    }
                }
            });

        } catch (error) {
            logger.error('Search messages failed:', error);
            next(error);
        }
    }

    /**
     * 导出聊天数据
     */
    public async exportChatData(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const {
                format = 'csv',
                chatId,
                dateFrom,
                dateTo,
                role
            } = req.query;

            // 构建查询条件
            const query: any = {};
            if (chatId) query.chatId = chatId;
            if (role && Object.values(Role).includes(role as Role)) {
                query.role = role;
            }

            if (dateFrom || dateTo) {
                query.createdTime = {};
                if (dateFrom) query.createdTime.$gte = new Date(dateFrom as string);
                if (dateTo) query.createdTime.$lte = new Date(dateTo as string);
            }

            // 查询数据
            const messages = await ChatMessageModel.find(query)
                .sort({ createdTime: 1 })
                .lean();

            // 格式化数据
            const formattedData = messages.map(message => ({
                id: message.id,
                chatId: message.chatId,
                role: message.role,
                content: message.content,
                isComplete: message.isComplete,
                createdTime: new Date(message.createdTime).toISOString()
            }));

            if (format === 'json') {
                res.json({
                    success: true,
                    data: formattedData,
                    total: formattedData.length
                });
            } else if (format === 'csv') {
                const csv = this.generateCSV(formattedData);
                const filename = `chat_export_${new Date().toISOString().split('T')[0]}.csv`;

                res.setHeader('Content-Type', 'text/csv');
                res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
                res.send(csv);
            } else {
                res.status(400).json({
                    success: false,
                    message: 'Unsupported format. Use json or csv.'
                });
            }

            logger.info(`Exported ${formattedData.length} chat messages in ${format} format`);

        } catch (error) {
            logger.error('Export chat data failed:', error);
            next(error);
        }
    }

    /**
     * 高亮关键词
     */
    private highlightKeyword(content: string, keyword: string): string {
        const regex = new RegExp(`(${keyword})`, 'gi');
        return content.replace(regex, '<mark>$1</mark>');
    }

    /**
     * 生成CSV格式数据
     */
    private generateCSV(data: any[]): string {
        if (data.length === 0) return '';

        const fields = ['id', 'chatId', 'role', 'content', 'isComplete', 'createdTime'];

        // 生成表头
        const headers = fields.map(field => `"${field}"`).join(',');

        // 生成数据行
        const rows = data.map(row => {
            return fields.map(field => {
                const value = row[field];
                if (value === null || value === undefined) return '""';
                if (typeof value === 'string') {
                    return `"${value.replace(/"/g, '""')}"`;
                }
                return `"${value}"`;
            }).join(',');
        });

        return [headers, ...rows].join('\n');
    }
}

export default new ChatMessageController();
