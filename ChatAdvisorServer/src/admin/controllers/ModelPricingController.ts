import { Request, Response, NextFunction } from 'express';
import { validationResult } from 'express-validator';
import AIServiceModel from '../../models/AIServiceModel';
import AIServiceConfig from '../../models/AIServiceConfig';
import OpenAIClientFactory from '../../services/OpenAIClientFactory';
import AIConfigCache from '../../services/AIConfigCache';
import { logger } from '../../business/logger';

/**
 * 基于 AIServiceModel 的价格管理控制器
 * 统一使用 AI 配置管理系统的数据
 */
class ModelPricingController {
    /**
     * 获取模型价格列表
     */
    public async getModelPricings(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { 
                page = 1, 
                limit = 20, 
                search, 
                configId,
                sortBy = 'createdAt',
                sortOrder = 'desc'
            } = req.query;

            const query: any = { isActive: true };
            
            // 如果指定了配置ID，只查询该配置下的模型
            if (configId) {
                query.configId = configId;
            }
            
            // 搜索条件
            if (search) {
                query.$or = [
                    { modelName: { $regex: search, $options: 'i' } },
                    { displayName: { $regex: search, $options: 'i' } }
                ];
            }

            const total = await AIServiceModel.countDocuments(query);
            const models = await AIServiceModel.find(query)
                .populate('configId', 'name provider baseURL')
                .sort({ [sortBy as string]: sortOrder === 'desc' ? -1 : 1 })
                .skip((parseInt(page as string) - 1) * parseInt(limit as string))
                .limit(parseInt(limit as string));

            // 获取当前启用的模型ID
            const { default: activeAIConfigManager } = await import('../../services/ActiveAIConfigManager');
            const activeConfig = await activeAIConfigManager.getActiveConfig();
            const currentModelId = activeConfig?.modelId?.toString();

            const formattedModels = models.map(model => ({
                _id: model._id,
                modelName: model.modelName,
                displayName: model.displayName,
                description: model.description,
                maxTokens: model.maxTokens,
                supportedFeatures: model.supportedFeatures,
                pricing: model.pricing,
                configId: model.configId,
                configName: (model.configId as any)?.name,
                provider: (model.configId as any)?.provider,
                isCurrentModel: model._id.toString() === currentModelId,
                isActive: model.isActive,
                sortOrder: model.sortOrder,
                createdAt: model.createdAt,
                updatedAt: model.updatedAt
            }));

            res.json({
                success: true,
                data: {
                    items: formattedModels,
                    total,
                    page: parseInt(page as string),
                    limit: parseInt(limit as string),
                    totalPages: Math.ceil(total / parseInt(limit as string))
                }
            });

        } catch (error) {
            logger.error('Get model pricings failed:', error);
            next(error);
        }
    }

    /**
     * 更新模型价格
     */
    public async updateModelPricing(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const errors = validationResult(req);
            if (!errors.isEmpty()) {
                res.status(400).json({
                    success: false,
                    message: 'Validation failed',
                    errors: errors.array()
                });
                return;
            }

            const { id } = req.params;
            const { pricing, displayName, description, maxTokens } = req.body;

            const model = await AIServiceModel.findById(id);
            if (!model) {
                res.status(404).json({
                    success: false,
                    message: 'Model not found'
                });
                return;
            }

            // 更新价格信息
            if (pricing) {
                const DEFAULT_PRICE = 5 / 3000; // 5每3000字符
                model.pricing = {
                    inputPrice: pricing.inputPrice === 0 ? DEFAULT_PRICE : pricing.inputPrice,
                    outputPrice: pricing.outputPrice === 0 ? DEFAULT_PRICE : pricing.outputPrice,
                    currency: pricing.currency || 'CNY'
                };
                // 标记为手动修改价格
                model.isPricingCustomized = true;
            }

            // 更新其他信息
            if (displayName !== undefined) model.displayName = displayName;
            if (description !== undefined) model.description = description;
            if (maxTokens !== undefined) model.maxTokens = maxTokens;

            await model.save();

            logger.info(`模型价格更新成功: ${model.modelName}`);

            res.json({
                success: true,
                data: model,
                message: 'Model pricing updated successfully'
            });

        } catch (error) {
            logger.error('Update model pricing failed:', error);
            next(error);
        }
    }

    /**
     * 批量修复零价格模型
     */
    public async fixZeroModelPricing(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            // 使用与配置文件一致的默认价格：基于 gpt-4o-mini 的价格配置
            // profitRate = 0.045, exchangeRate = 100
            // inputPrice = (0.004 + 0.045) * 100 = 4.9 每3000字符
            // outputPrice = (0.004 * 3 + 0.045) * 100 = 5.7 每3000字符
            const DEFAULT_INPUT_PRICE = 4.9 / 3000; // 与配置文件保持一致
            const DEFAULT_OUTPUT_PRICE = 5.7 / 3000; // 与配置文件保持一致

            // 查找价格为0且未被手动修改的模型
            const zeroModels = await AIServiceModel.find({
                isActive: true,
                isPricingCustomized: { $ne: true }, // 只修复未被手动修改的模型
                $or: [
                    { 'pricing.inputPrice': 0 },
                    { 'pricing.outputPrice': 0 }
                ]
            });

            if (zeroModels.length === 0) {
                res.json({
                    success: true,
                    message: '未找到需要修复的零价格模型（已排除手动修改的模型）',
                    data: {
                        fixed: 0,
                        defaultInputPrice: DEFAULT_INPUT_PRICE,
                        defaultOutputPrice: DEFAULT_OUTPUT_PRICE
                    }
                });
                return;
            }

            // 批量更新
            const bulkOps = zeroModels.map(model => ({
                updateOne: {
                    filter: { _id: model._id },
                    update: {
                        $set: {
                            'pricing.inputPrice': model.pricing.inputPrice === 0 ? DEFAULT_INPUT_PRICE : model.pricing.inputPrice,
                            'pricing.outputPrice': model.pricing.outputPrice === 0 ? DEFAULT_OUTPUT_PRICE : model.pricing.outputPrice
                        }
                    }
                }
            }));

            const result = await AIServiceModel.bulkWrite(bulkOps);

            logger.info(`批量修复零价格模型完成: ${result.modifiedCount} 个模型`);

            res.json({
                success: true,
                message: `成功修复 ${result.modifiedCount} 个零价格模型（已排除手动修改的模型）`,
                data: {
                    fixed: result.modifiedCount,
                    defaultInputPrice: DEFAULT_INPUT_PRICE,
                    defaultOutputPrice: DEFAULT_OUTPUT_PRICE,
                    models: zeroModels.map(m => m.modelName)
                }
            });

        } catch (error) {
            logger.error('Fix zero model pricing failed:', error);
            next(error);
        }
    }

    /**
     * 同步模型并设置默认价格
     */
    public async syncModelsWithPricing(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { configId } = req.body;

            if (!configId) {
                res.status(400).json({
                    success: false,
                    message: 'Config ID is required'
                });
                return;
            }

            // 获取配置信息
            const config = await AIServiceConfig.findById(configId).select('+apiKey');
            if (!config) {
                res.status(404).json({
                    success: false,
                    message: 'AI service config not found'
                });
                return;
            }

            // 测试连接并获取模型列表
            const testResult = await OpenAIClientFactory.testConnection(config);
            if (!testResult.success || !testResult.models) {
                res.status(400).json({
                    success: false,
                    message: `Unable to get model list: ${testResult.error}`
                });
                return;
            }

            const DEFAULT_PRICE = 5 / 3000; // 5每3000字符
            const syncResults = {
                created: 0,
                updated: 0,
                errors: 0,
                models: [] as any[]
            };

            // 获取现有模型，保留已手动修改价格的模型
            const existingModels = await AIServiceModel.find({ configId }).lean();
            const existingModelMap = new Map();
            existingModels.forEach(model => {
                existingModelMap.set(model.modelName, model);
            });

            // 删除现有模型
            await AIServiceModel.deleteMany({ configId });

            // 创建新模型
            for (let i = 0; i < testResult.models.length; i++) {
                try {
                    const modelName = testResult.models[i];
                    // 获取对应的模型详细信息
                    const modelInfo = testResult.modelsInfo ?
                        testResult.modelsInfo.find(info => info.id === modelName) : null;

                    // 检查是否存在已有的模型配置
                    const existingModel = existingModelMap.get(modelName);
                    const isPricingCustomized = existingModel?.isPricingCustomized || false;
                    const isActive = existingModel?.isActive || false; // 保留原有的启用状态

                    // 如果价格已被手动修改，保留原价格；否则使用默认价格
                    const pricing = isPricingCustomized && existingModel ? {
                        inputPrice: existingModel.pricing.inputPrice,
                        outputPrice: existingModel.pricing.outputPrice,
                        currency: existingModel.pricing.currency
                    } : {
                        inputPrice: DEFAULT_PRICE,
                        outputPrice: DEFAULT_PRICE,
                        currency: 'CNY'
                    };

                    const model = new AIServiceModel({
                        configId,
                        modelName,
                        displayName: modelName,
                        maxTokens: 4096,
                        supportedFeatures: ['text'],
                        pricing,
                        modelInfo: modelInfo, // 存储完整的模型信息
                        isPricingCustomized, // 保留自定义标记
                        isActive, // 保留原有的启用状态，新模型默认为false
                        sortOrder: i
                    });

                    await model.save();
                    syncResults.created++;
                    syncResults.models.push({
                        modelName,
                        action: isPricingCustomized ? 'preserved_custom_pricing' : 'created',
                        pricing: model.pricing,
                        modelInfo: modelInfo,
                        isPricingCustomized
                    });

                } catch (error) {
                    logger.error(`同步模型失败: ${testResult.models[i]}`, error);
                    syncResults.errors++;
                    syncResults.models.push({
                        modelName: testResult.models[i],
                        action: 'error',
                        error: error.message
                    });
                }
            }

            logger.info(`模型同步完成: 创建 ${syncResults.created}, 错误 ${syncResults.errors}`);

            res.json({
                success: true,
                message: `Successfully synced ${testResult.models.length} models`,
                data: {
                    configName: config.name,
                    totalModels: testResult.models.length,
                    defaultPrice: DEFAULT_PRICE,
                    results: syncResults
                }
            });

        } catch (error) {
            logger.error('Sync models with pricing failed:', error);
            next(error);
        }
    }

    /**
     * 批量禁用所有模型
     */
    public async disableAllModels(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { configId } = req.params;

            if (!configId) {
                res.status(400).json({
                    success: false,
                    message: '配置ID不能为空'
                });
                return;
            }

            // 批量禁用指定配置下的所有模型
            const result = await AIServiceModel.updateMany(
                { configId },
                { $set: { isActive: false } }
            );

            logger.info(`批量禁用模型: 配置ID ${configId}, 影响 ${result.modifiedCount} 个模型`);

            // 清除模型缓存
            await AIConfigCache.clearModelCache(configId);

            res.json({
                success: true,
                message: `成功禁用 ${result.modifiedCount} 个模型`,
                data: {
                    configId,
                    disabledCount: result.modifiedCount
                }
            });

        } catch (error) {
            logger.error('批量禁用模型失败:', error);
            res.status(500).json({
                success: false,
                message: '批量禁用模型失败',
                error: error.message
            });
        }
    }

    /**
     * 切换模型启用状态
     */
    public async toggleModelStatus(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { modelId } = req.params;

            if (!modelId) {
                res.status(400).json({
                    success: false,
                    message: '模型ID不能为空'
                });
                return;
            }

            const model = await AIServiceModel.findById(modelId);
            if (!model) {
                res.status(404).json({
                    success: false,
                    message: '模型不存在'
                });
                return;
            }

            // 切换状态
            model.isActive = !model.isActive;
            await model.save();

            logger.info(`模型状态已切换: ${model.modelName}, 新状态: ${model.isActive ? '启用' : '禁用'}`);

            // 清除模型缓存
            await AIConfigCache.clearModelCache(model.configId.toString());

            res.json({
                success: true,
                message: `模型已${model.isActive ? '启用' : '禁用'}`,
                data: {
                    modelId: model._id,
                    modelName: model.modelName,
                    isActive: model.isActive
                }
            });

        } catch (error) {
            logger.error('切换模型状态失败:', error);
            res.status(500).json({
                success: false,
                message: '切换模型状态失败',
                error: error.message
            });
        }
    }

    /**
     * 获取当前启用模型的价格信息
     */
    public async getCurrentModelPricing(req: Request, res: Response, next: NextFunction): Promise<void> {
        try {
            const { default: activeAIConfigManager } = await import('../../services/ActiveAIConfigManager');
            const activeConfig = await activeAIConfigManager.getActiveConfig();

            if (!activeConfig || !activeConfig.modelId) {
                res.status(404).json({
                    success: false,
                    message: 'No active model configuration found'
                });
                return;
            }

            const model = await AIServiceModel.findById(activeConfig.modelId)
                .populate('configId', 'name provider baseURL');

            if (!model) {
                res.status(404).json({
                    success: false,
                    message: 'Active model not found'
                });
                return;
            }

            res.json({
                success: true,
                data: {
                    model,
                    configName: (model.configId as any)?.name,
                    provider: (model.configId as any)?.provider,
                    isActiveModel: true
                }
            });

        } catch (error) {
            logger.error('Get current model pricing failed:', error);
            next(error);
        }
    }
}

export default new ModelPricingController();
