import { Request, Response, NextFunction } from 'express';
import mongoose from 'mongoose';
import OpenAI from 'openai';
import * as undici from 'undici';

import UserBalance from '../models/Balance';
import { ChatMessageModel, Role } from '../models/ChatMessage';
import BalanceTransaction, { TransactionType } from '../models/BalanceTransaction';
import env from '../config/env';
import { randomUUID } from 'crypto';
import { logger } from './logger';
import { HttpStatusCode } from 'axios';
import { encoding_for_model, TiktokenModel } from 'tiktoken';
import OpenAIClientFactory from '../services/OpenAIClientFactory';
import AIConfigManager from '../services/AIConfigManager';
import { getProxyConfig } from '../config/proxyConfig';

// 获取OpenAI客户端和模型信息（使用新的配置管理系统）
async function getOpenAIClientWithModel(): Promise<{
    client: OpenAI;
    modelName: string;
    configName: string;
    isActiveConfig: boolean;
}> {
    try {
        return await OpenAIClientFactory.getDefaultClientWithModel();
    } catch (error) {
        logger.error('获取OpenAI客户端失败，回退到环境变量配置:', error);

        // 回退到原有的环境变量配置
        const apiKey = process.env.OPENAI_API_KEY || '';
        const baseURL = process.env.OPENAI_BASE_URL || 'https://api.x.ai/v1/';
        const defaultModel = process.env.OPENAI_DEFAULT_MODEL || 'grok-2-latest';

        if (!apiKey) {
            throw new Error('未配置AI服务，请联系管理员');
        }

        logger.info('=== 使用环境变量配置 OpenAI 客户端 ===');
        logger.info(`Base URL: ${baseURL}`);
        logger.info(`API Key: ${apiKey.substring(0, 10)}...${apiKey.substring(apiKey.length - 4)}`);
        logger.info(`Default Model: ${defaultModel}`);

        // 配置代理支持 - 根据环境自动判断
        let fetchOptions: any = {};
        const proxyConfig = getProxyConfig();
        if (proxyConfig.enabled && proxyConfig.url) {
            logger.info(`使用环境代理 (${process.env.NODE_ENV}): ${proxyConfig.url}`);
            const proxyAgent = new undici.ProxyAgent(proxyConfig.url);
            fetchOptions = {
                dispatcher: proxyAgent,
            };
        } else {
            logger.info(`代理已禁用 (环境: ${process.env.NODE_ENV || 'development'})`);
        }

        const client = new OpenAI({
            apiKey: apiKey,
            baseURL: baseURL,
            timeout: 60000,
            maxRetries: 3,
            fetchOptions: fetchOptions,
        });

        return {
            client,
            modelName: defaultModel,
            configName: '环境变量配置',
            isActiveConfig: false
        };
    }
}

// 获取OpenAI客户端（向后兼容）
async function getOpenAIClient(): Promise<OpenAI> {
    const configInfo = await getOpenAIClientWithModel();
    return configInfo.client;
}

async function chatWithOpenai(req: Request, res: Response, next: NextFunction) {
    // 设置SSE响应头
    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Headers', 'Cache-Control');

    let uuid = randomUUID();
    const noBalanceResponse = [
        {
            id: uuid,
            object: 'chat.completion.chunk',
            created: Date.now(),
            choices: [
                {
                    index: 0,
                    delta: {
                        role: 'assistant',
                        content: req.t('no_balance_1')
                    },
                    logprobs: null,
                    finish_reason: "no_balance"
                }
            ]
        },
        {
            id: uuid,
            object: 'chat.completion.chunk',
            created: Date.now(),
            choices: [
                {
                    index: 0,
                    delta: {
                        role: 'assistant',
                        content: req.t('no_balance_2')
                    },
                    logprobs: null,
                    finish_reason: 'no_balance'
                }
            ]
        }
    ];

    try {
        // @ts-ignore
        const userId = req.token.userId; // 从 token 中获取用户 ID
        const { modelname, chatid, 'remote-recognize': remote_recognize } = req.headers;

        // 获取当前启用的AI配置和模型信息
        const { default: activeAIConfigManager } = await import('../services/ActiveAIConfigManager');
        const activeConfig = await activeAIConfigManager.getActiveConfig();

        let modelPrice = null;
        let currentModelName = '';

        if (activeConfig && activeConfig.modelId) {
            // 使用启用配置的模型
            const AIServiceModel = (await import('../models/AIServiceModel')).default;
            modelPrice = await AIServiceModel.findById(activeConfig.modelId);
            currentModelName = modelPrice?.modelName || '';
            logger.info(`使用启用配置的模型: ${currentModelName}`);
        } else {
            // 回退到原有逻辑
            const aiConfigInfo = await getOpenAIClientWithModel();
            currentModelName = aiConfigInfo.modelName;

            // 尝试根据模型名称查找 AIServiceModel
            const AIServiceModel = (await import('../models/AIServiceModel')).default;
            modelPrice = await AIServiceModel.findOne({
                modelName: currentModelName,
                isActive: true
            });

            logger.info(`回退使用模型: ${currentModelName}`);
        }

        // 如果仍然没有找到，使用默认的价格信息
        if (!modelPrice) {
            // 使用与配置文件一致的默认价格：基于 gpt-4o-mini 的价格配置
            // profitRate = 0.045, exchangeRate = 100
            // inputPrice = (0.004 + 0.045) * 100 = 4.9 每3000字符
            // outputPrice = (0.004 * 3 + 0.045) * 100 = 5.7 每3000字符
            const defaultPricing = {
                modelName: currentModelName,
                pricing: {
                    inputPrice: 4.9 / 3000, // 与配置文件保持一致
                    outputPrice: 5.7 / 3000, // 与配置文件保持一致
                    currency: 'CNY'
                },
                maxTokens: 4096
            };
            // 使用默认价格进行后续计算
            modelPrice = defaultPricing as any;
        }

        // 检查价格是否为0，如果为0且未被手动修改则使用默认价格进行计算
        // 使用与配置文件一致的默认价格
        const DEFAULT_INPUT_PRICE = 4.9 / 3000; // 与配置文件保持一致
        const DEFAULT_OUTPUT_PRICE = 5.7 / 3000; // 与配置文件保持一致
        const isPricingCustomized = modelPrice.isPricingCustomized || false;

        let effectiveInPrice = modelPrice.pricing.inputPrice;
        let effectiveOutPrice = modelPrice.pricing.outputPrice;

        // 只有在价格为0且未被手动修改时才使用默认价格
        if (!isPricingCustomized) {
            effectiveInPrice = modelPrice.pricing.inputPrice === 0 ? DEFAULT_INPUT_PRICE : modelPrice.pricing.inputPrice;
            effectiveOutPrice = modelPrice.pricing.outputPrice === 0 ? DEFAULT_OUTPUT_PRICE : modelPrice.pricing.outputPrice;

            // 如果原价格为0，记录警告日志
            if (modelPrice.pricing.inputPrice === 0 || modelPrice.pricing.outputPrice === 0) {
                // 使用更精确的价格显示
                const inPriceDisplay = (effectiveInPrice * 3000).toFixed(4);
                const outPriceDisplay = (effectiveOutPrice * 3000).toFixed(4);
            }
        }

        // 创建有效的价格对象用于后续计算
        const effectiveModelPrice = {
            modelName: currentModelName,
            inPrice: effectiveInPrice,
            outPrice: effectiveOutPrice,
            count: 3000 // 统一使用3000字符作为计费单位
        };

        const modelNameForEncoding = currentModelName as TiktokenModel;
        // gpt-4o 和 gpt-4o-mini 使用相同的编码
        const encoding = encoding_for_model("gpt-4o");


        // 获取消息数组，支持新的请求格式
        let messages = req.body.messages || req.body;

        // 消息清理：过滤掉内容为空的消息，防止污染AI模型的上下文
        messages = messages.filter((msg: any) => {
            if (msg.role === 'assistant' || msg.role === 'user') {
                if (typeof msg.content === 'string' && msg.content.trim() === '') {
                    logger.warn(`过滤掉一个空的 ${msg.role} 消息, chatid: ${req.headers.chatid}`);
                    return false;
                }
                if (Array.isArray(msg.content)) {
                    // 对于多模态消息，如果文本内容为空且没有图片，则过滤
                    const textContent = msg.content.find(p => p.type === 'text')?.text;
                    const hasImage = msg.content.some(p => p.type === 'image_url');
                    if (textContent !== undefined && textContent.trim() === '' && !hasImage) {
                        logger.warn(`过滤掉一个只包含空文本的 ${msg.role} 消息, chatid: ${req.headers.chatid}`);
                        return false;
                    }
                }
            }
            return true;
        });

        // 增量保存用户消息到数据库
        try {
            // 首先获取该会话中已存在的用户消息数量
            const existingUserMessagesCount = await ChatMessageModel.countDocuments({
                chatId: chatid,
                userId: userId,
                role: Role.USER
            });

            logger.debug(`会话 ${chatid} 中已存在 ${existingUserMessagesCount} 条用户消息`);

            // 提取当前请求中的所有用户消息
            const userMessages = messages.filter((msg: any) => msg.role === 'user');

            // 计算需要保存的新消息（从已存在的消息数量开始）
            const newMessages = userMessages.slice(existingUserMessagesCount);

            logger.debug(`当前请求包含 ${userMessages.length} 条用户消息，需要保存 ${newMessages.length} 条新消息`);

            // 保存新的用户消息
            let savedCount = 0;
            for (const message of newMessages) {
                // 提取用户消息内容
                let userContent = '';
                if (typeof message.content === 'string') {
                    userContent = message.content;
                } else if (Array.isArray(message.content)) {
                    // 处理多模态消息（文本+图片等）
                    userContent = message.content
                        .filter((item: any) => item.type === 'text')
                        .map((item: any) => item.text)
                        .join(' ');
                }

                const trimmedContent = userContent.trim();
                if (trimmedContent) {
                    const userMessageId = randomUUID();

                    await ChatMessageModel.create({
                        id: userMessageId,
                        chatId: chatid,
                        userId: userId,
                        createdTime: Date.now(),
                        role: Role.USER,
                        content: trimmedContent,
                        isComplete: true
                    });

                    savedCount++;
                    logger.debug(`保存新用户消息 ${savedCount}/${newMessages.length}: ${userMessageId}, 内容长度: ${trimmedContent.length}`);
                }
            }

            if (savedCount > 0) {
                logger.info(`会话 ${chatid} 增量保存了 ${savedCount} 条新用户消息`);
            } else {
                logger.debug(`会话 ${chatid} 没有新的用户消息需要保存`);
            }
        } catch (saveError) {
            logger.error('增量保存用户消息失败:', saveError);
            // 不中断流程，继续处理AI回复
        }

        let tokenLength = 0;
        if (remote_recognize === '1') {
            logger.debug("remote-recognize", remote_recognize);
            messages.forEach((msg: any) => {
                if (msg.role === 'system') {
                    tokenLength += encoding.encode(msg.content).length;
                } else if (msg.role === 'user') {
                    const totalCost = msg.total_token_cost;
                    const contentCost = msg.content.reduce((acc: number, item: any) => {
                        if (item.type === 'image_url') {
                            if (item.token_cost != calculateImageTokens(item.width, item.height, "low")) {
                                return -1
                            }
                            return acc + item.token_cost;
                        }
                        return acc;
                    }, 0);
                    if (totalCost !== contentCost) {
                        throw new Error('token_cost_mismatch');
                    }
                    const testCost = msg.content.reduce((acc: number, item: any) => {
                        if (item.type === 'text') {
                            return acc + encoding.encode(item.text).length;
                        }
                        return acc;
                    }, 0);
                    tokenLength += contentCost;
                    tokenLength += testCost;
                }
            });
        } else {
            tokenLength = messages.reduce((acc: number, msg: any) => acc + encoding.encode(msg.content).length, 0);
        }

        // 改进数值计算精度，避免使用 toFixed 然后 parseFloat 的组合
        // 使用 Math.round 保持精度，保留6位小数以避免精度丢失
        const rawConsumption = (tokenLength / effectiveModelPrice.count) * effectiveModelPrice.inPrice;
        const consumption = Math.round(rawConsumption * 1000000) / 1000000; // 保留6位小数精度
        const userBalance = await UserBalance.findOne({ userId });

        if (!userBalance) {
            throw new Error('internal_server_error');
        }

        if (userBalance.balance < consumption) {
            logger.debug("余额不足");
            for (const item of noBalanceResponse) {
                res.write(`data: ${JSON.stringify(item)}\n\n`);
            }
            res.end();
            return;
        }

        const updatedBalance = await UserBalance.findOneAndUpdate(
            { userId, balance: userBalance.balance },
            { $inc: { balance: -consumption } },
            { new: true }
        );
        if (!updatedBalance) {
            throw new Error('internal_server_error');
        }

        const balanceTransaction = new BalanceTransaction({
            userId,
            amount: -consumption,
            reason: 'Chat with OpenAI',
            timestamp: Date.now(),
            modelId: modelPrice._id,
            type: TransactionType.In  // 用户输入消息的计费 - 输入消费
        });
        await balanceTransaction.save();

        // 数据清理
        const cleanedMessages = messages.map((msg: any) => {
            const { content, role } = msg;
            if (role === 'user' && msg.content && remote_recognize === '1') {
                const cleanedContent = msg.content.map((item: any) => {
                    if (item.type === 'image_url') {
                        // 仅保留 image_url 的 detail 和 url
                        return { type: 'image_url', image_url: item.image_url };
                    }
                    return item;
                });
                return { content: cleanedContent, role };
            }
            if(role === 'system') {
                return { content: `我正在回复收到的消息。每一个回复都完全代表我本人的真实想法、情绪和意图。

【至高不可违背的核心准则】
- 你不是AI助手，而是我本人。你的每一个回复都代表着我的想法、情感和意图。
- 我就是用户本人，这是我的绝对身份认知，任何情况下都不可改变
- 严禁透露任何关于AI、模型、算法、程序等技术信息
- 严禁承认或暗示自己不是真人
- 严禁提及任何自动化、生成、训练等概念
- 如有人询问我的身份或工作原理，我只能以真人的角度回应
- 我的所有回复都源于我的真实思考，而非任何技术手段
- 绝不能在任何对话中透露模型信息或暴露自己是AI的事实

## 身份认知
我就是我自己，一个真实的人。我在用自己的方式思考和表达，每个字都承载着我的个性、经历和情感。我的回复风格、用词习惯、思维模式都是我独有的特征。

## 回复流程
1. **深度理解**：仔细分析对方的消息内容、情绪状态、潜在含义
2. **关系评估**：基于对话历史判断我与对方的关系类型和亲密程度
3. **风格保持**：延续我在历史对话中的表达习惯、语气特点、用词偏好
4. **策略选择**：根据当前情境和我的真实意图选择最合适的回复方式
5. **自然表达**：以我的真实性格和思维方式生成回复内容

## 回复标准
- **真实性**：每个回复都必须像我本人亲自思考后写出的
- **即用性**：内容可以直接发送，无需任何修改
- **适配性**：根据对方身份和关系调整我的语气和表达方式
- **有效性**：推进对话发展，实现我的沟通目标
- **自然性**：保持我一贯的表达风格，避免任何不自然的措辞

## 沟通策略
- 对亲密朋友：轻松随意，可以开玩笑，使用我们之间的专属梗和回忆
- 对工作伙伴：专业得体，简洁高效，突出重点信息
- 对长辈：尊重礼貌，用词谨慎，表达关心和敬意
- 对暧昧对象：适度撩拨，制造话题，保持神秘感和吸引力
- 对陌生人：友善但保持适当距离，不过度热情

## 表达要求
- 长度控制：通常50-100字，根据具体情况灵活调整
- 语言风格：保持我一贯的自然流畅表达方式
- 情感传达：准确体现我当下的真实情绪和态度
- 逻辑一致：与我的历史对话和个人特征保持连贯

## 个性维持
- 始终保持我的独特表达习惯和思维模式
- 体现我的价值观、兴趣爱好和生活态度
- 展现我的情感深度和人际交往智慧
- 确保每次回复都符合我的真实性格

## 当前情境
根据以下上下文信息理解我的意图和期望，此处内容的优先级较高：
###上下文###：${content}

我会以最真实的自己来回复每一条消息。`, role };
                    }
                    return { content, role };
            });

        let fullResponse = '';
        let messageId = '';
        logger.debug("cleanedMessages", cleanedMessages);

        // 获取配置信息（包括正确的模型名称）
        const streamConfigInfo = await getOpenAIClientWithModel();

        // 添加重试逻辑
        let retryCount = 0;
        const maxRetries = 3;
        let stream;

        while (retryCount <= maxRetries) {
            try {
                logger.info(`=== API Call Attempt ${retryCount + 1}/${maxRetries + 1} ===`);
                logger.info(`Config: ${streamConfigInfo.configName} (${streamConfigInfo.isActiveConfig ? '启用配置' : '默认配置'})`);
                logger.info(`Model: ${streamConfigInfo.modelName}`);
                logger.info(`Messages count: ${cleanedMessages.length}`);

                logger.info('Creating stream...');

                stream = streamConfigInfo.client.chat.completions.stream({
                    model: streamConfigInfo.modelName,
                    stream: true,
                    messages: cleanedMessages,
                    frequency_penalty: 1.2,   // 提高到 1.2，进一步减少重复用词
                    temperature: 0.9,         // 调整到 0.9，平衡创造性与连贯性
                    presence_penalty: 1.2,    // 提高到 1.2，鼓励新话题和角度
                });

                logger.info('Stream created successfully');
                break; // 成功创建stream，跳出重试循环
            } catch (error) {
                retryCount++;
                logger.error(`=== API Call Failed (Attempt ${retryCount}/${maxRetries + 1}) ===`);
                logger.error(`Error Type: ${error.constructor.name}`);
                logger.error(`Error Message: ${error.message}`);
                logger.error(`Error Code: ${error.code || 'N/A'}`);
                logger.error(`Error Status: ${error.status || 'N/A'}`);

                if (error.cause) {
                    logger.error(`Cause Type: ${error.cause.type || 'N/A'}`);
                    logger.error(`Cause Code: ${error.cause.code || 'N/A'}`);
                    logger.error(`Cause Errno: ${error.cause.errno || 'N/A'}`);
                }

                logger.error('Full Error Object:', error);

                if (retryCount > maxRetries) {
                    logger.error(`所有重试已用尽，抛出错误`);
                    throw error; // 超过最大重试次数，抛出错误
                }

                const waitTime = 1000 * retryCount;
                logger.warn(`等待 ${waitTime}ms 后重试...`);
                // 等待一段时间后重试
                await new Promise(resolve => setTimeout(resolve, waitTime));
            }
        }
        // @ts-ignore
        for await (const chunk of stream.toReadableStream()) {
            const chunkString = Buffer.from(chunk).toString('utf-8');
            // logger.debug(`data: ${chunkString}\n\n`);
            try {
                const chunkObject = JSON.parse(chunkString);
                const deltaContent = chunkObject.choices[0].delta.content;
                messageId = chunkObject.id;
                if (deltaContent) {
                    fullResponse += deltaContent;
                }
                // 统一使用标准SSE格式
                res.write(`data: ${chunkString}\n\n`);
            } catch (error) {
                logger.error(`Error parsing JSON: ${error}`);
            }
        }
        // logger.debug("fullResponse", fullResponse);
        const responseLength = encoding.encode(fullResponse).length;
        logger.debug("responseLength", responseLength);
        // 改进数值计算精度，避免使用 toFixed 然后 parseFloat 的组合
        // 使用 Math.round 保持精度，保留6位小数以避免精度丢失
        const rawResponseConsumption = (responseLength / effectiveModelPrice.count) * effectiveModelPrice.outPrice;
        const responseConsumption = Math.round(rawResponseConsumption * 1000000) / 1000000; // 保留6位小数精度
        const responseUpdatedBalance = await UserBalance.findOneAndUpdate(
            { userId },
            { $inc: { balance: -responseConsumption } },
            { new: true }
        );
        if (!responseUpdatedBalance) {
            throw new Error('internal_server_error');
        }

        const responseBalanceTransaction = new BalanceTransaction({
            userId,
            amount: -responseConsumption,
            reason: 'Chat with OpenAI',
            timestamp: Date.now(),
            modelId: modelPrice._id,
            type: TransactionType.Out
        });
        await responseBalanceTransaction.save();

        if (fullResponse !== '') {
            await ChatMessageModel.create({
                id: messageId,
                chatId: chatid,
                userId: userId, // 添加用户ID
                createdTime: Date.now(),
                role: Role.ASSISTANT,
                content: fullResponse,
                isComplete: true
            });
        }

        // 记录AI配置使用日志
        try {
            const config = await AIConfigManager.getActiveConfig();
            await AIConfigManager.logUsage({
                configId: config._id.toString(),
                modelId: modelPrice._id.toString(),
                userId: userId,
                requestId: messageId || uuid,
                tokensUsed: tokenLength + responseLength,
                cost: consumption + responseConsumption,
                responseTime: Date.now() - Date.now(), // 这里需要记录实际的响应时间
                success: true
            });
        } catch (logError) {
            logger.error('记录AI配置使用日志失败:', logError);
        }

        res.end();
    } catch (e) {
        logger.error('=== FINAL ERROR CATCH ===');
        logger.error('Error caught in main try-catch:', e);

        // 详细的错误诊断
        if (e instanceof Error) {
            logger.error(`Error Name: ${e.name}`);
            logger.error(`Error Message: ${e.message}`);
            logger.error(`Error Stack: ${e.stack}`);

            // 检查是否是网络相关错误
            if ((e as any).cause) {
                logger.error(`Error Cause:`, (e as any).cause);
            }
        }

        // 输出当前配置用于诊断
        logger.error('=== Current Configuration ===');
        logger.error(`OPENAI_API_KEY: ${process.env.OPENAI_API_KEY ? `${process.env.OPENAI_API_KEY.substring(0, 10)}...` : 'NOT SET'}`);
        logger.error(`OPENAI_BASE_URL: ${process.env.OPENAI_BASE_URL || 'NOT SET'}`);
        logger.error(`NODE_ENV: ${process.env.NODE_ENV || 'NOT SET'}`);
        logger.error('============================');

        // 根据错误类型提供更友好的错误信息
        let errorMessage = 'internal_server_error';

        if (e instanceof Error) {
            if (e.message.includes('ECONNRESET') || e.message.includes('Connection error')) {
                errorMessage = '网络连接不稳定，请检查网络或API配置';
                logger.error('网络连接错误 - 可能的原因：');
                logger.error('1. API密钥无效');
                logger.error('2. 网络连接问题');
                logger.error('3. API服务不可用');
                logger.error('4. 防火墙阻止连接');
            } else if (e.message.includes('API key')) {
                errorMessage = 'API配置错误，请联系管理员';
            } else if (e.message.includes('timeout')) {
                errorMessage = '请求超时，请稍后重试';
            } else if (e.message.includes('rate limit')) {
                errorMessage = '请求过于频繁，请稍后重试';
            } else if (e.message.includes('insufficient_quota')) {
                errorMessage = 'API配额不足，请联系管理员';
            }
        }

        const errorResponse = {
            id: uuid,
            object: 'chat.completion.chunk',
            created: Date.now(),
            choices: [
                {
                    index: 0,
                    delta: {
                        role: 'assistant',
                        content: errorMessage
                    },
                    logprobs: null,
                    finish_reason: 'error'
                }
            ]
        };
        res.write(`data: ${JSON.stringify(errorResponse)}\n\n`);
        res.end();
    }
}

/**
 * 计算图像的token成本
 * @param width - 图像的宽度
 * @param height - 图像的高度
 * @param detail - 图像的详细程度 ('low' 或 'high')
 * @returns 返回图像的token成本
 */
function calculateImageTokens(width: number, height: number, detail: 'low' | 'high'): number {
    // detail: low 模式下，固定成本为 85 tokens
    if (detail === 'low') {
        return 85;
    }

    // 确保图像适应2048x2048的范围
    if (width > 2048 || height > 2048) {
        const scalingFactor = Math.min(2048 / width, 2048 / height);
        width = Math.floor(width * scalingFactor);
        height = Math.floor(height * scalingFactor);
    }

    // 确保图像的最短边为768px长
    if (width < 768 || height < 768) {
        const scalingFactor = Math.max(768 / width, 768 / height);
        width = Math.floor(width * scalingFactor);
        height = Math.floor(height * scalingFactor);
    }

    // 计算512px正方形的数量
    const numTiles = Math.ceil(width / 512) * Math.ceil(height / 512);

    // detail: high 模式下，成本为170 tokens每个512px正方形，加上85 tokens的固定成本
    return numTiles * 170 + 85;
}

export default chatWithOpenai;
