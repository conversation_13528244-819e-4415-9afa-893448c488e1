import { logger } from '../business/logger';

/**
 * 代理配置接口
 */
export interface ProxyConfig {
    enabled: boolean;
    httpProxy?: string;
    httpsProxy?: string;
    url?: string; // 统一代理URL，优先使用此配置
}

/**
 * 获取当前环境的代理配置
 * 根据 NODE_ENV 环境变量决定是否启用代理
 * 
 * @returns ProxyConfig 代理配置对象
 */
export function getProxyConfig(): ProxyConfig {
    const nodeEnv = process.env.NODE_ENV || 'development';
    const isDevelopment = nodeEnv === 'development';
    
    // 只在开发环境中启用代理
    if (!isDevelopment) {
        logger.info(`当前环境: ${nodeEnv}, 代理已禁用`);
        return {
            enabled: false
        };
    }
    
    // 开发环境中检查代理配置
    const httpProxy = process.env.HTTP_PROXY;
    const httpsProxy = process.env.HTTPS_PROXY;
    
    if (!httpProxy && !httpsProxy) {
        logger.info('开发环境未配置代理');
        return {
            enabled: false
        };
    }
    
    // 优先使用 HTTPS_PROXY，回退到 HTTP_PROXY
    const proxyUrl = httpsProxy || httpProxy;
    
    logger.info(`开发环境代理已启用: ${proxyUrl}`);
    
    return {
        enabled: true,
        httpProxy,
        httpsProxy,
        url: proxyUrl
    };
}

/**
 * 检查代理是否应该启用
 * 
 * @returns boolean 是否启用代理
 */
export function isProxyEnabled(): boolean {
    return getProxyConfig().enabled;
}

/**
 * 获取代理URL（如果启用）
 * 
 * @returns string | undefined 代理URL
 */
export function getProxyUrl(): string | undefined {
    const config = getProxyConfig();
    return config.enabled ? config.url : undefined;
}

/**
 * 为环境变量设置代理（仅在开发环境）
 * 主要用于兼容一些依赖环境变量的库
 */
export function setupEnvironmentProxy(): void {
    const config = getProxyConfig();
    
    if (!config.enabled) {
        // 确保在非开发环境中清除代理环境变量
        delete process.env.HTTP_PROXY;
        delete process.env.HTTPS_PROXY;
        delete process.env.http_proxy;
        delete process.env.https_proxy;
        return;
    }
    
    // 在开发环境中设置代理环境变量
    if (config.httpProxy) {
        process.env.HTTP_PROXY = config.httpProxy;
        process.env.http_proxy = config.httpProxy;
    }
    
    if (config.httpsProxy) {
        process.env.HTTPS_PROXY = config.httpsProxy;
        process.env.https_proxy = config.httpsProxy;
    }
    
    logger.info('代理环境变量已设置');
}

/**
 * 获取代理配置的详细信息（用于日志和调试）
 */
export function getProxyInfo(): {
    environment: string;
    enabled: boolean;
    proxyUrl?: string;
    httpProxy?: string;
    httpsProxy?: string;
} {
    const nodeEnv = process.env.NODE_ENV || 'development';
    const config = getProxyConfig();
    
    return {
        environment: nodeEnv,
        enabled: config.enabled,
        proxyUrl: config.url,
        httpProxy: config.httpProxy,
        httpsProxy: config.httpsProxy
    };
}
