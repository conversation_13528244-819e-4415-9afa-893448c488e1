import express from 'express';
import cors from 'cors';
import { logger } from './business/logger';
import { engine } from 'express-handlebars';
import { ensureData } from './business/ensureData';
import connectDatabase from './config/database';
import { configureRoutes } from './routers';
import errorHandler from './middlewares/errorHandler';
import logRequestsIntoDB from './middlewares/logHandler';
import logReceiveHandler from './middlewares/printReceiveHandler';
import verifyTokenHandler from './middlewares/verifyTokenHandler';
import env from './config/env';
import { setupEnvironmentProxy, getProxyInfo } from './config/proxyConfig';
const rootDir = process.cwd();
import 'dotenv/config';

// 处理未捕获的异常
process.on('uncaughtException', (err) => {
    logger.error('Uncaught Exception:', err);
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});

// 初始化代理配置
setupEnvironmentProxy();
const proxyInfo = getProxyInfo();
logger.info('=== 代理配置信息 ===');
logger.info(`环境: ${proxyInfo.environment}`);
logger.info(`代理状态: ${proxyInfo.enabled ? '启用' : '禁用'}`);
if (proxyInfo.enabled) {
    logger.info(`代理URL: ${proxyInfo.proxyUrl}`);
}

const app = express();

// 1. 基础中间件配置（在所有路由之前）
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// 2. CORS配置 - 修复CORS问题，添加前端地址和缺失的域名
const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || [
    'http://localhost:3000', 
    'http://localhost:54001',  // admin-frontend 本地开发端口
    'https://advisor.sanva.tk', 
    'https://advisor.sanva.top', 
    'https://admin.sanva.top',
    'https://admin.sanva.tk'   // admin-frontend 生产环境域名
];
app.use(cors({
    origin: (origin, callback) => {
        // 如果没有origin（同源请求），也允许
        if (!origin) {
            return callback(null, true);
        }
        // 检查origin是否在允许列表中
        if (allowedOrigins.includes(origin)) {
            return callback(null, true);
        }
        // 如果是localhost（开发环境），也允许
        if (origin.includes('localhost')) {
            return callback(null, true);
        }
        return callback(new Error('Not allowed by CORS'));
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'App-Version', 'OS-Version', 'Device-Model', 'Time-Zone', 'Network-Type', 'Screen-Resolution', 'Device-Orientation', 'local'],
    optionsSuccessStatus: 200 // 确保OPTIONS请求返回200而不是204
}));

// 3. 静态文件和模板引擎
app.use(express.static(rootDir + '/src/public'));
app.engine('handlebars', engine());
app.set('view engine', 'handlebars');
// {{ AURA-X: Modify - 修复生产环境views目录路径，优先使用构建后的dist/src/views. Approval: mcp-feedback-enhanced(ID:20250806160). }}
const viewsPath = process.env.NODE_ENV === 'production'
    ? rootDir + '/dist/src/views'  // 生产环境使用构建后的views
    : rootDir + '/src/views';      // 开发环境使用源码views
app.set('views', viewsPath);

// 4. 全局日志中间件
if (env.isRelease) {
    app.use(logRequestsIntoDB);
} else {
    app.use(logReceiveHandler);
}

connectDatabase().then(() => {
    ensureData();

    // 5. 新React管理后台静态文件服务（在token验证之前）
    app.use('/admin', express.static(rootDir + '/admin-frontend/dist'));

    // 管理后台SPA路由 - 所有/admin路径都返回React应用的index.html（在token验证之前）
    app.get('/admin*', (req, res) => {
        res.sendFile(rootDir + '/admin-frontend/dist/index.html');
    });

    // 6. Token验证中间件（在需要认证的路由之前）
    app.use(verifyTokenHandler);

    // 7. 后台管理API路由（需要在token验证之后，但有自己的认证）
    const adminRoutes = require('./admin/routes').default;
    app.use('/api/admin', adminRoutes);

    // 8. 配置所有业务路由
    configureRoutes(app);

    // 9. 全局错误处理中间件（必须在最后）
    app.use(errorHandler);

    const port = parseInt(process.env.PORT || '3000');
    const nodeEnv = process.env.NODE_ENV || 'development';

    app.listen(port, () => {
        logger.info(`Server started on port ${port} in ${nodeEnv} mode`);
        logger.info(`Admin panel available at http://localhost:${port}/admin`);
    });
});
