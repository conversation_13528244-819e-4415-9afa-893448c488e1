import React, { useState } from 'react';
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/react/24/outline';
import clsx from 'clsx';

interface ExpandableTextProps {
  text: string;
  maxLength?: number;
  className?: string;
  highlightedText?: string; // 用于搜索高亮
}

const ExpandableText: React.FC<ExpandableTextProps> = ({
  text,
  maxLength = 100,
  className = '',
  highlightedText
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  
  // 如果文本长度小于等于最大长度，直接显示
  if (text.length <= maxLength) {
    return (
      <div className={className}>
        {highlightedText ? (
          <span dangerouslySetInnerHTML={{ __html: highlightedText }} />
        ) : (
          <span>{text}</span>
        )}
      </div>
    );
  }

  const displayText = isExpanded ? text : text.slice(0, maxLength);
  const displayHighlightedText = isExpanded 
    ? highlightedText 
    : highlightedText?.slice(0, maxLength);

  return (
    <div className={clsx('space-y-2', className)}>
      <div className="relative">
        {highlightedText ? (
          <span dangerouslySetInnerHTML={{ 
            __html: displayHighlightedText || displayText 
          }} />
        ) : (
          <span>{displayText}</span>
        )}
        {!isExpanded && (
          <span className="text-gray-400">...</span>
        )}
      </div>
      
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        className="inline-flex items-center space-x-1 text-xs text-blue-600 hover:text-blue-800 transition-colors"
      >
        {isExpanded ? (
          <>
            <ChevronUpIcon className="h-3 w-3" />
            <span>收起</span>
          </>
        ) : (
          <>
            <ChevronDownIcon className="h-3 w-3" />
            <span>展开 ({text.length} 字符)</span>
          </>
        )}
      </button>
    </div>
  );
};

export default ExpandableText;
