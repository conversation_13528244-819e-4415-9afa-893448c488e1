import React, { useState, useEffect, useCallback } from 'react';
import { format } from 'date-fns';
import Table from '@/components/common/Table';
import Button from '@/components/common/Button';
import Input from '@/components/common/Input';
import Badge from '@/components/common/Badge';
import Pagination from '@/components/common/Pagination';
import Modal, { ModalBody, ModalFooter } from '@/components/common/Modal';
import ExpandableText from '@/components/common/ExpandableText';
import { useTable } from '@/hooks/useTable';
import { useApi } from '@/hooks/useApi';
import { chatService } from '@/services/chat';
import { ChatMessage, ChatMessageListParams, ChatRole } from '@/types/chat';
import { TableColumn } from '@/types/common';
import clsx from 'clsx';

const MessageList: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [total, setTotal] = useState(0);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [viewModalOpen, setViewModalOpen] = useState(false);
  const [selectedMessage, setSelectedMessage] = useState<ChatMessage | null>(null);
  const [filters, setFilters] = useState({
    role: '',
    isComplete: '',
    chatId: '',
    dateFrom: '',
    dateTo: '',
  });

  // 表格状态管理
  const {
    tableState,
    pagination,
    sorting,
    searching,
    getQueryParams,
  } = useTable({
    initialPageSize: 20,
    initialSortBy: 'createdTime',
    initialSortOrder: 'desc',
  });

  // API调用
  const {
    loading,
    execute: fetchMessages,
  } = useApi(chatService.getMessages);

  const {
    loading: deleting,
    execute: deleteMessage,
  } = useApi(chatService.deleteMessage);

  // 加载消息列表
  const loadMessages = useCallback(async () => {
    try {
      const params: ChatMessageListParams = {
        ...getQueryParams(),
        ...filters,
      };
      // 清理空值
      Object.keys(params).forEach(key => {
        if (params[key as keyof ChatMessageListParams] === '') {
          delete params[key as keyof ChatMessageListParams];
        }
      });

      const result = await fetchMessages(params);
      setMessages(result.items);
      setTotal(result.total);
    } catch (error) {
      console.error('加载消息列表失败:', error);
    }
  }, [fetchMessages, getQueryParams, filters]);

  // 初始加载和参数变化时重新加载
  useEffect(() => {
    loadMessages();
  }, [loadMessages]);

  // 删除消息
  const handleDelete = async () => {
    if (!selectedMessage) return;
    
    try {
      await deleteMessage(selectedMessage._id);
      setDeleteModalOpen(false);
      setSelectedMessage(null);
      loadMessages(); // 重新加载列表
    } catch (error) {
      console.error('删除消息失败:', error);
    }
  };

  // 筛选器变化处理
  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  // 获取角色显示文本和样式
  const getRoleInfo = (role: ChatRole) => {
    const roleMap = {
      user: { text: '用户', variant: 'primary' as const },
      assistant: { text: 'AI助手', variant: 'success' as const },
      other: { text: '其他', variant: 'default' as const },
    };
    return roleMap[role] || roleMap.other;
  };

  // 表格列定义
  const columns: TableColumn<ChatMessage>[] = [
    {
      key: 'id',
      title: '消息ID',
      dataIndex: 'id',
      width: 120,
      render: (id: string) => (
        <span className="text-xs font-mono text-gray-600">
          {id.substring(0, 8)}...
        </span>
      ),
    },
    {
      key: 'chatId',
      title: '会话ID',
      dataIndex: 'chatId',
      width: '10%',
      render: (chatId: string) => (
        <span className="text-xs font-mono text-gray-600">
          {chatId.substring(0, 8)}...
        </span>
      ),
    },
    {
      key: 'role',
      title: '角色',
      dataIndex: 'role',
      width: '8%',
      render: (role: ChatRole) => {
        const roleInfo = getRoleInfo(role);
        return <Badge variant={roleInfo.variant}>{roleInfo.text}</Badge>;
      },
    },
    {
      key: 'content',
      title: '消息内容',
      dataIndex: 'content',
      width: '40%', // 设置固定宽度比例
      allowWrap: true, // 允许换行
      render: (content: string, record: ChatMessage) => (
        <div className="min-w-0 max-w-lg">
          <ExpandableText
            text={content}
            maxLength={150}
            highlightedText={record.highlightedContent}
            className="text-sm text-gray-900"
          />
        </div>
      ),
    },
    {
      key: 'isComplete',
      title: '状态',
      dataIndex: 'isComplete',
      width: '8%',
      render: (isComplete: boolean) => (
        <Badge variant={isComplete ? 'success' : 'warning'}>
          {isComplete ? '已完成' : '进行中'}
        </Badge>
      ),
    },
    {
      key: 'messageType',
      title: '类型',
      dataIndex: 'messageType',
      width: '6%',
      render: (messageType: string = 'text') => {
        const typeMap = {
          text: '文本',
          image: '图片',
          audio: '音频',
          video: '视频',
          file: '文件',
          location: '位置',
          contact: '联系人',
          system: '系统',
        };
        return (
          <span className="text-xs text-gray-600">
            {typeMap[messageType as keyof typeof typeMap] || messageType}
          </span>
        );
      },
    },
    {
      key: 'createdTime',
      title: '创建时间',
      dataIndex: 'createdTime',
      width: '15%',
      sortable: true,
      render: (createdTime: string) => (
        <span className="text-sm text-gray-500">
          {format(new Date(createdTime), 'yyyy-MM-dd HH:mm:ss')}
        </span>
      ),
    },
    {
      key: 'actions',
      title: '操作',
      dataIndex: '_id',
      width: '13%',
      render: (id: string, record: ChatMessage) => (
        <div className="flex space-x-2">
          <Button
            size="sm"
            variant="secondary"
            onClick={() => {
              setSelectedMessage(record);
              setViewModalOpen(true);
            }}
          >
            查看
          </Button>
          <Button
            size="sm"
            variant="danger"
            onClick={() => {
              setSelectedMessage(record);
              setDeleteModalOpen(true);
            }}
          >
            删除
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900">聊天消息管理</h1>
          <p className="mt-1 text-sm text-gray-600">
            查看和管理所有聊天消息记录
          </p>
        </div>
        <div className="flex space-x-3">
          <Button variant="secondary">
            导出数据
          </Button>
          <Button variant="secondary">
            批量删除
          </Button>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="bg-white p-4 rounded-lg shadow">
        <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
          <Input
            placeholder="搜索消息内容..."
            value={searching.search}
            onChange={(e) => searching.onSearch(e.target.value)}
            leftIcon={
              <svg className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            }
          />
          
          <Input
            placeholder="会话ID"
            value={filters.chatId}
            onChange={(e) => handleFilterChange('chatId', e.target.value)}
          />

          <select
            value={filters.role}
            onChange={(e) => handleFilterChange('role', e.target.value)}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
          >
            <option value="">所有角色</option>
            <option value="user">用户</option>
            <option value="assistant">AI助手</option>
            <option value="other">其他</option>
          </select>

          <select
            value={filters.isComplete}
            onChange={(e) => handleFilterChange('isComplete', e.target.value)}
            className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
          >
            <option value="">所有状态</option>
            <option value="true">已完成</option>
            <option value="false">进行中</option>
          </select>

          <Input
            type="date"
            placeholder="开始日期"
            value={filters.dateFrom}
            onChange={(e) => handleFilterChange('dateFrom', e.target.value)}
          />

          <Input
            type="date"
            placeholder="结束日期"
            value={filters.dateTo}
            onChange={(e) => handleFilterChange('dateTo', e.target.value)}
          />
        </div>
      </div>

      {/* 消息表格 */}
      <Table
        columns={columns}
        data={messages}
        loading={loading}
        emptyText="暂无聊天消息"
        rowKey="_id"
      />

      {/* 分页 */}
      <Pagination
        current={pagination.current}
        total={total}
        pageSize={pagination.pageSize}
        onChange={pagination.onChange}
        onPageSizeChange={pagination.onPageSizeChange}
        showSizeChanger
        showQuickJumper
        showTotal
      />

      {/* 查看详情弹窗 */}
      <Modal
        isOpen={viewModalOpen}
        onClose={() => setViewModalOpen(false)}
        title="消息详情"
        size="lg"
      >
        <ModalBody>
          {selectedMessage && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">消息ID</label>
                  <p className="text-sm text-gray-900 font-mono">{selectedMessage.id}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">会话ID</label>
                  <p className="text-sm text-gray-900 font-mono">{selectedMessage.chatId}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">角色</label>
                  <div className="mt-1">
                    <Badge variant={getRoleInfo(selectedMessage.role).variant}>
                      {getRoleInfo(selectedMessage.role).text}
                    </Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">状态</label>
                  <div className="mt-1">
                    <Badge variant={selectedMessage.isComplete ? 'success' : 'warning'}>
                      {selectedMessage.isComplete ? '已完成' : '进行中'}
                    </Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">消息类型</label>
                  <p className="text-sm text-gray-900">{selectedMessage.messageType || 'text'}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">创建时间</label>
                  <p className="text-sm text-gray-900">
                    {format(new Date(selectedMessage.createdTime), 'yyyy-MM-dd HH:mm:ss')}
                  </p>
                </div>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700">消息内容</label>
                <div className="mt-2 p-4 bg-gray-50 rounded-md border">
                  <pre className="text-sm text-gray-900 whitespace-pre-wrap break-words">
                    {selectedMessage.content}
                  </pre>
                </div>
              </div>

              {selectedMessage.finishReason && (
                <div>
                  <label className="text-sm font-medium text-gray-700">完成原因</label>
                  <p className="text-sm text-gray-900">{selectedMessage.finishReason}</p>
                </div>
              )}
            </div>
          )}
        </ModalBody>
        <ModalFooter>
          <Button
            variant="secondary"
            onClick={() => setViewModalOpen(false)}
          >
            关闭
          </Button>
        </ModalFooter>
      </Modal>

      {/* 删除确认弹窗 */}
      <Modal
        isOpen={deleteModalOpen}
        onClose={() => setDeleteModalOpen(false)}
        title="确认删除"
        size="sm"
      >
        <ModalBody>
          <p className="text-sm text-gray-500">
            确定要删除这条消息吗？此操作不可撤销。
          </p>
          {selectedMessage && (
            <div className="mt-3 p-3 bg-gray-50 rounded-md">
              <p className="text-xs text-gray-600">消息内容预览：</p>
              <p className="text-sm text-gray-900 mt-1 truncate">
                {selectedMessage.content}
              </p>
            </div>
          )}
        </ModalBody>
        <ModalFooter>
          <Button
            variant="secondary"
            onClick={() => setDeleteModalOpen(false)}
          >
            取消
          </Button>
          <Button
            variant="danger"
            loading={deleting}
            onClick={handleDelete}
          >
            确认删除
          </Button>
        </ModalFooter>
      </Modal>
    </div>
  );
};

export default MessageList;
