import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import {
  ArrowLeftIcon,
  ChatBubbleLeftIcon,
  ClockIcon,
  CpuChipIcon,
  DocumentTextIcon,
  CalendarIcon,
  UserIcon,
  EyeIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import Button from '@/components/common/Button';
import Badge from '@/components/common/Badge';
import Loading from '@/components/common/Loading';
import { chatService } from '@/services/chat';
import { ChatSession, ChatMessage } from '@/types/chat';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';

const SessionDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [session, setSession] = useState<ChatSession | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [loading, setLoading] = useState(true);
  const [messagesLoading, setMessagesLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取会话详情
  const fetchSessionDetail = async () => {
    if (!id) return;
    
    try {
      setLoading(true);
      setError(null);
      
      // 从会话列表中查找对应的会话信息
      const sessionsResponse = await chatService.getSessions({
        page: 1,
        limit: 100
      });
      
      const foundSession = sessionsResponse.items.find(
        (s: ChatSession) => s.chatId === id
      );
      
      if (!foundSession) {
        setError('会话不存在');
        return;
      }
      
      setSession(foundSession);
      
      // 获取会话消息
      await fetchSessionMessages(id);
      
    } catch (err: any) {
      console.error('获取会话详情失败:', err);
      setError(err.response?.data?.message || '获取会话详情失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取会话消息
  const fetchSessionMessages = async (chatId: string) => {
    try {
      setMessagesLoading(true);
      
      const response = await chatService.getMessages({
        page: 1,
        limit: 1000,
        chatId: chatId
      });

      setMessages(response.items || []);
      
    } catch (err: any) {
      console.error('获取会话消息失败:', err);
    } finally {
      setMessagesLoading(false);
    }
  };

  useEffect(() => {
    fetchSessionDetail();
  }, [id]);

  // 格式化时长
  const formatDuration = (minutes: number) => {
    if (minutes < 1) return '< 1分钟';
    if (minutes < 60) return `${Math.round(minutes)}分钟`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = Math.round(minutes % 60);
    if (remainingMinutes === 0) return `${hours}小时`;
    return `${hours}小时${remainingMinutes}分钟`;
  };

  // 格式化消息内容
  const formatMessageContent = (content: string, maxLength: number = 200) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loading />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">{error}</div>
        <Button
          variant="secondary"
          onClick={() => navigate('/chat/sessions')}
          icon={<ArrowLeftIcon className="h-4 w-4" />}
        >
          返回会话列表
        </Button>
      </div>
    );
  }

  if (!session) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-600 mb-4">会话不存在</div>
        <Button
          variant="secondary"
          onClick={() => navigate('/chat/sessions')}
          icon={<ArrowLeftIcon className="h-4 w-4" />}
        >
          返回会话列表
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-4">
            <Button
              variant="secondary"
              size="sm"
              onClick={() => navigate('/chat/sessions')}
              icon={<ArrowLeftIcon className="h-4 w-4" />}
            >
              返回列表
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">会话详情</h1>
              <p className="text-sm text-gray-500">查看会话的详细信息和消息记录</p>
            </div>
          </div>
          
          <div className="flex space-x-2">
            <Link to={`/chat/messages?chatId=${session.chatId}`}>
              <Button
                variant="secondary"
                size="sm"
                icon={<EyeIcon className="h-4 w-4" />}
              >
                查看消息
              </Button>
            </Link>
          </div>
        </div>

        {/* 会话基本信息 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <ChatBubbleLeftIcon className="h-5 w-5 text-blue-500" />
              <span className="text-sm font-medium text-gray-700">会话ID</span>
            </div>
            <p className="text-sm text-gray-900 font-mono">{session.chatId}</p>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <DocumentTextIcon className="h-5 w-5 text-green-500" />
              <span className="text-sm font-medium text-gray-700">消息数量</span>
            </div>
            <p className="text-2xl font-bold text-gray-900">{session.messageCount}</p>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <ClockIcon className="h-5 w-5 text-orange-500" />
              <span className="text-sm font-medium text-gray-700">会话时长</span>
            </div>
            <p className="text-lg font-semibold text-gray-900">
              {formatDuration(session.duration)}
            </p>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <CpuChipIcon className="h-5 w-5 text-purple-500" />
              <span className="text-sm font-medium text-gray-700">使用模型</span>
            </div>
            <p className="text-sm text-gray-900">{session.model || 'unknown'}</p>
          </div>
        </div>

        {/* 时间信息 */}
        <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <CalendarIcon className="h-5 w-5 text-blue-500" />
              <span className="text-sm font-medium text-gray-700">开始时间</span>
            </div>
            <p className="text-sm text-gray-900">
              {format(new Date(session.firstMessageTime), 'yyyy-MM-dd HH:mm:ss', { locale: zhCN })}
            </p>
          </div>

          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <CalendarIcon className="h-5 w-5 text-green-500" />
              <span className="text-sm font-medium text-gray-700">最后活动</span>
            </div>
            <p className="text-sm text-gray-900">
              {format(new Date(session.lastMessageTime), 'yyyy-MM-dd HH:mm:ss', { locale: zhCN })}
            </p>
          </div>
        </div>
      </div>

      {/* 消息列表 */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">消息记录</h2>
          <p className="text-sm text-gray-500">该会话的所有消息记录</p>
        </div>

        <div className="p-6">
          {messagesLoading ? (
            <div className="flex items-center justify-center h-32">
              <Loading />
            </div>
          ) : messages.length === 0 ? (
            <div className="text-center py-12">
              <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">暂无消息</h3>
              <p className="mt-1 text-sm text-gray-500">该会话还没有任何消息记录</p>
            </div>
          ) : (
            <div className="space-y-4">
              {messages.map((message, index) => (
                <div
                  key={message._id}
                  className={`p-4 rounded-lg border ${
                    message.role === 'user' 
                      ? 'bg-blue-50 border-blue-200 ml-8' 
                      : 'bg-gray-50 border-gray-200 mr-8'
                  }`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <UserIcon className={`h-4 w-4 ${
                        message.role === 'user' ? 'text-blue-500' : 'text-gray-500'
                      }`} />
                      <span className={`text-sm font-medium ${
                        message.role === 'user' ? 'text-blue-700' : 'text-gray-700'
                      }`}>
                        {message.role === 'user' ? '用户' : 'AI助手'}
                      </span>
                      <Badge
                        variant={message.isComplete ? 'success' : 'warning'}
                        size="sm"
                      >
                        {message.isComplete ? '已完成' : '进行中'}
                      </Badge>
                    </div>
                    <span className="text-xs text-gray-500">
                      {format(new Date(message.createdAt), 'HH:mm:ss')}
                    </span>
                  </div>
                  
                  <div className="text-sm text-gray-900 whitespace-pre-wrap">
                    {message.content}
                  </div>
                  
                  {message.messageType && message.messageType !== 'text' && (
                    <div className="mt-2">
                      <Badge variant="secondary" size="sm">
                        {message.messageType}
                      </Badge>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SessionDetail;
