import React from 'react';
import { Routes, Route, useLocation, useNavigate } from 'react-router-dom';
import {
  ListBulletIcon,
  Squares2X2Icon,
  ChartBarIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline';
import Button from '@/components/common/Button';
import MessageList from './MessageList';
import SessionList from './SessionList';
import SessionDetail from './SessionDetail';
import ChatStats from './ChatStats';
import HierarchicalChatView from './HierarchicalChatView';

const Chat: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();

  // 根据当前路径确定活跃的视图
  const getActiveView = () => {
    const path = location.pathname;
    if (path.includes('/messages')) return 'messages';
    if (path.includes('/sessions')) return 'sessions';
    if (path.includes('/stats')) return 'stats';
    return 'hierarchical';
  };

  const activeView = getActiveView();

  return (
    <div className="space-y-6">
      {/* 视图切换器 */}
      <div className="bg-white shadow rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-medium text-gray-900">聊天管理</h2>
            <p className="text-sm text-gray-600">管理用户聊天记录、会话和消息</p>
          </div>
          <div className="flex space-x-2">
            <Button
              variant={activeView === 'hierarchical' ? 'primary' : 'secondary'}
              onClick={() => navigate('/chat')}
              icon={<Squares2X2Icon className="h-4 w-4" />}
              size="sm"
            >
              层级视图
            </Button>
            <Button
              variant={activeView === 'messages' ? 'primary' : 'secondary'}
              onClick={() => navigate('/chat/messages')}
              icon={<ListBulletIcon className="h-4 w-4" />}
              size="sm"
            >
              消息列表
            </Button>
            <Button
              variant={activeView === 'sessions' ? 'primary' : 'secondary'}
              onClick={() => navigate('/chat/sessions')}
              icon={<ChatBubbleLeftRightIcon className="h-4 w-4" />}
              size="sm"
            >
              会话列表
            </Button>
            <Button
              variant={activeView === 'stats' ? 'primary' : 'secondary'}
              onClick={() => navigate('/chat/stats')}
              icon={<ChartBarIcon className="h-4 w-4" />}
              size="sm"
            >
              统计分析
            </Button>
          </div>
        </div>
      </div>

      {/* 路由内容区域 */}
      <Routes>
        <Route index element={<HierarchicalChatView />} />
        <Route path="messages" element={<MessageList />} />
        <Route path="sessions" element={<SessionList />} />
        <Route path="sessions/:id" element={<SessionDetail />} />
        <Route path="stats" element={<ChatStats />} />
      </Routes>
    </div>
  );
};

export default Chat;
