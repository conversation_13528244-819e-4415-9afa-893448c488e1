// 通用类型定义

export interface ApiResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
  error?: string;
}

export interface PaginationParams {
  page: number;
  limit: number;
}

export interface PaginationResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface SortParams {
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface SearchParams {
  search?: string;
}

export interface DateRangeParams {
  dateFrom?: string;
  dateTo?: string;
}

export interface TableColumn<T = any> {
  key: string;
  title: string;
  dataIndex: keyof T;
  width?: string | number;
  sortable?: boolean;
  allowWrap?: boolean; // 允许文本换行
  render?: (value: any, record: T, index: number) => React.ReactNode;
}

export interface SelectOption {
  label: string;
  value: string | number;
}

export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'select' | 'textarea' | 'date' | 'checkbox';
  required?: boolean;
  placeholder?: string;
  options?: SelectOption[];
  validation?: {
    pattern?: RegExp;
    min?: number;
    max?: number;
    minLength?: number;
    maxLength?: number;
  };
}

export interface LoadingState {
  loading: boolean;
  error: string | null;
}

export interface User {
  _id: string;
  userId: string;
  username?: string;
  email: string;
  fullName?: string;
  avatar?: string;
  role: 'user' | 'admin' | 'super_admin';
  status: 'active' | 'inactive' | 'suspended';
  balance: number;
  totalSpent?: number;
  lastLoginAt?: string;
  lastLoginIP?: string;
  emailVerified: boolean;
  phoneVerified?: boolean;
  isVip?: boolean;
  vipExpiredAt?: string;
  hasPurchase: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AuthUser {
  _id: string;
  email: string;
  fullName?: string;
  role: 'admin' | 'super_admin';
  avatar?: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
  twoFactorToken?: string;
}

export interface AuthResponse {
  user: AuthUser;
  token: string;
  refreshToken?: string;
}
